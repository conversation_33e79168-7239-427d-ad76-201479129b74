package jsonnet

import (
	"strings"
	"testing"

	"github.com/google/go-cmp/cmp"
	"golang.org/x/tools/godoc/vfs/mapfs"
)

func TestVM(t *testing.T) {
	t.<PERSON>llel()

	vm := NewVM(nil, NewVFSImporter(mapfs.New(map[string]string{
		"main.jsonnet": `
			local lib = import '//lib/lib.jsonnet';
			function(a=0) {
				foo: 'bar',
				baz: lib.mult(a, 2),
			}
		`,
		"lib/lib.jsonnet": `
			{
				mult: function(a, b) a * b,
			}
		`,
		"lib/k8s.jsonnet": `
			function(pfx) [{
				kind: 'Secret',
				metadata: {
					name: pfx + 'sec0',
					namespace: 'default',
				},
				type: 'Opaque',
				data: {
					foo0: 'bar0',
				},
			}]
		`,
	})))

	t.Run("eval", func(t *testing.T) {
		t.<PERSON>()

		got, err := vm.Eval("main.jsonnet", nil, "")
		got = strings.ReplaceAll(strings.ReplaceAll(got, " ", ""), "\n", "")
		if err != nil {
			t.Fatal(err)
		}
		if want := `{"baz":0,"foo":"bar"}`; got != want {
			t.Errorf("got %v, want %v.", got, want)
		}
	})

	t.Run("eval-tla", func(t *testing.T) {
		t.Parallel()

		got, err := vm.Eval("main.jsonnet", map[string]string{"a": "7"}, "")
		got = strings.ReplaceAll(strings.ReplaceAll(got, " ", ""), "\n", "")
		if err != nil {
			t.Fatal(err)
		}
		if want := `{"baz":14,"foo":"bar"}`; got != want {
			t.Errorf("got %v, want %v.", got, want)
		}
	})

	t.Run("eval-snippet", func(t *testing.T) {
		t.Parallel()

		got, err := vm.Eval("snippet.jsonnet", nil, `
			local lib = import '//lib/lib.jsonnet';
			{
				foo: 'BAR',
				baz: lib.mult(2, 21),
			}
		`)
		got = strings.ReplaceAll(strings.ReplaceAll(got, " ", ""), "\n", "")
		if err != nil {
			t.Fatal(err)
		}
		if want := `{"baz":42,"foo":"BAR"}`; got != want {
			t.Errorf("got %v, want %v.", got, want)
		}
	})

	t.Run("eval-json", func(t *testing.T) {
		t.Parallel()
		got := struct {
			Foo string `json:"foo"`
			Baz int    `json:"baz"`
		}{}
		if err := vm.EvalJSON("main.jsonnet", map[string]string{"a": "7"}, "", &got); err != nil {
			t.Fatal(err)
		}
		want := struct {
			Foo string `json:"foo"`
			Baz int    `json:"baz"`
		}{
			Foo: "bar",
			Baz: 14,
		}
		if diff := cmp.Diff(want, got); diff != "" {
			t.Errorf("-want +got:\n%s", diff)
		}
	})

	t.Run("eval-k8s", func(t *testing.T) {
		t.Parallel()

		gots, err := vm.EvalK8s("lib/k8s.jsonnet", map[string]string{"pfx": "'pfx0-'"}, "")
		if err != nil {
			t.Fatal(err)
		}

		if l := len(gots); l != 1 {
			t.Fatalf("got %d objects, want 1.", l)
		}
		got := gots[0]

		if got, want := got.Kind(), "Secret"; got != want {
			t.Errorf("kind: got %v, want %v.", got, want)
		}
		if got, want := got.Version(), "v1"; got != want {
			t.Errorf("version: got %v, want %v.", got, want)
		}
		if got, want := "default", got.Namespace(); got != want {
			t.Errorf("namespace: got %v, want %v.", got, want)
		}
		if got, want := "pfx0-sec0", got.Name(); got != want {
			t.Errorf("name: got %v, want %v.", got, want)
		}
	})

	t.Run("eval-json-e2e-error", func(t *testing.T) {
		t.Parallel()

		err := vm.EvalJSON("snippet.jsonnet", nil, `
			error 'Forced test error!'
		`, nil)
		if err == nil {
			t.Errorf("got no error, want error.")
		} else if !strings.Contains(err.Error(), "RUNTIME ERROR: Forced test error!") {
			t.Errorf("got error %v, want error containing 'RUNTIME ERROR: Forced test error!'.", err)
		}
	})

	t.Run("eval-k8s-e2e-error", func(t *testing.T) {
		t.Parallel()

		_, err := vm.EvalK8s("snippet.jsonnet", nil, `
			error 'Forced test error!'
		`)
		if err == nil {
			t.Errorf("got no error, want error.")
		} else if !strings.Contains(err.Error(), "RUNTIME ERROR: Forced test error!") {
			t.Errorf("got error %v, want error containing 'RUNTIME ERROR: Forced test error!'.", err)
		}
	})
}
