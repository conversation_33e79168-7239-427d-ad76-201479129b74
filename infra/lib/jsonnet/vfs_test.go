package jsonnet

import (
	"strings"
	"testing"

	"github.com/google/go-jsonnet"
	"golang.org/x/tools/godoc/vfs/mapfs"
)

func TestVFSImporter(t *testing.T) {
	t.<PERSON>()

	fs := mapfs.New(map[string]string{
		"path0/a":   "AAA",
		"path0/b":   "BbB",
		"path1/b":   "BBB",
		"path1/b/c": "CCC",
		"path2/b":   "bbb",
	})

	tests := map[string]struct {
		from     string
		p        string
		want     string
		wantFrom string
		wantErr  string
	}{
		"empty": {
			from:     "",
			p:        "",
			wantFrom: "",
			wantErr:  "file does not exist",
		},
		"absolute": {
			from:     "/path0/a",
			p:        "/path1/b",
			wantFrom: "/path1/b",
			want:     "BBB",
		},
		"absolute-cleaned": {
			from:     "/path0/a",
			p:        "/path1/../path2/b",
			wantFrom: "/path2/b",
			want:     "bbb",
		},
		"sibling": {
			from:     "/path0/a",
			p:        "b",
			wantFrom: "/path0/b",
			want:     "BbB",
		},
		"relative": {
			from:     "/path0/a",
			p:        "../path1/b/c",
			wantFrom: "/path1/b/c",
			want:     "CCC",
		},
		"relative-no-from": {
			from:     "",
			p:        "../path1/b",
			wantFrom: "",
			wantErr:  "file does not exist",
		},
		"absolute-build": {
			from:     "/path0/a",
			p:        "//path1/b",
			wantFrom: "/path1/b",
			want:     "BBB",
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			var i jsonnet.Importer = NewVFSImporter(fs)
			got, gotFrom, gotErr := i.Import(tc.from, tc.p)

			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got error %v, want no error.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got error %v, want error containing '%v'.", gotErr, tc.wantErr)
			}
			if got, want := gotFrom, tc.wantFrom; got != want {
				t.Errorf("from: got %v, want %v.", got, want)
			}
			if gotErr == nil {
				if got, want := got.String(), tc.want; got != want {
					t.Errorf("contents: got %v, want %v.", got, want)
				}
			}
		})
	}
}

func TestResolvePath(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		from string
		p    string
		want string
	}{
		"empty": {
			from: "",
			p:    "",
			want: ".",
		},
		"absolute": {
			from: "/path0/a",
			p:    "/path1/b",
			want: "/path1/b",
		},
		"absolute-cleaned": {
			from: "/path0/a",
			p:    "/path1/../path2/b",
			want: "/path2/b",
		},
		"sibling": {
			from: "/path0/a",
			p:    "b",
			want: "/path0/b",
		},
		"relative": {
			from: "/path0/a",
			p:    "../path1/b/c",
			want: "/path1/b/c",
		},
		"relative-no-from": {
			from: "",
			p:    "../path1/b",
			want: "../path1/b",
		},
		"absolute-build": {
			from: "/path0/a",
			p:    "//path1/b",
			want: "/path1/b",
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			if got, want := ResolvePath(tc.from, tc.p), tc.want; got != want {
				t.Errorf("ResolvePath(%s, %s): got %v, want %v.", tc.from, tc.p, got, want)
			}
		})
	}
}

func TestVNSImporter(t *testing.T) {
	t.Parallel()

	nsi := NewVNSImporter(nil)

	nsi.Bind("/root", mapfs.New(map[string]string{
		"foo/a":     "AAA1",
		"foo/bar/b": "BBB1",
		"baz/c":     "CCC1",
	}))

	nsi.Bind("/root/foo/bar", mapfs.New(map[string]string{
		"b": "BBB2",
		"d": "DDD2",
	}))

	nsi.Bind("/root/baz", mapfs.New(map[string]string{
		"c":     "CCC3",
		"e":     "EEE3",
		"qux/f": "FFF3",
	}))

	tests := map[string]struct {
		p       string
		want    string
		wantErr string
	}{
		"a": {
			p:    "/root/foo/a",
			want: "AAA1",
		},
		"b": {
			p:    "/root/foo/bar/b",
			want: "BBB2",
		},
		"c": {
			p:    "/root/baz/c",
			want: "CCC3",
		},
		"d": {
			p:    "/root/foo/bar/d",
			want: "DDD2",
		},
		"e": {
			p:    "/root/baz/e",
			want: "EEE3",
		},
		"f": {
			p:    "/root/baz/qux/f",
			want: "FFF3",
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			var i jsonnet.Importer = nsi
			got, _, gotErr := i.Import("", tc.p)

			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got error %v, want no error.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got error %v, want error containing '%v'.", gotErr, tc.wantErr)
			}
			if gotErr == nil {
				if got, want := got.String(), tc.want; got != want {
					t.Errorf("contents(%s): got %v, want %v.", tc.p, got, want)
				}
			}
		})
	}
}
