package jsonnet

import (
	"io/fs"
	"path/filepath"
	"sync"

	"github.com/google/go-jsonnet"
	"golang.org/x/tools/godoc/vfs"
)

// type assertion
var (
	_ jsonnet.Importer = &VFSImporter{}
	_ jsonnet.Importer = &VNSImporter{}
)

////////////////////////////////////////////////////////////////////////////////
//
// VFSImporter: A jsonnet.Importer for a vfs.FileSystem.
//

// VFSImporter implements the `jsonnet.Importer` interface but for the `vfs.FileSystem` interface instead of
// hard-coded to the local filesystem like the jsonnet package's built-in importer does. The `vfs.FileSystem`
// interface is more flexible and includes various backend implementations such as an in-memory filesystem.
type VFSImporter struct {
	fs      vfs.FileSystem
	cache   map[string]*cacheEntry
	cacheLk sync.Mutex
}

type cacheEntry struct {
	contents jsonnet.Contents
	err      error
}

func NewVFSImporter(fs vfs.FileSystem) *VFSImporter {
	return &VFSImporter{
		fs:    fs,
		cache: map[string]*cacheEntry{},
	}
}

func (i *VFSImporter) Import(importedFrom, importedPath string) (jsonnet.Contents, string, error) {
	absPath := ResolvePath(importedFrom, importedPath)

	i.cacheLk.Lock()
	defer i.cacheLk.Unlock()

	entry := i.cache[absPath]

	if entry == nil {
		entry = &cacheEntry{}
		i.cache[absPath] = entry

		if buf, err := vfs.ReadFile(i.fs, absPath); err != nil {
			entry.err = err
		} else {
			entry.contents = jsonnet.MakeContentsRaw(buf)
		}
	}

	if entry.err != nil {
		return jsonnet.Contents{}, "", entry.err
	} else {
		return entry.contents, absPath, nil
	}
}

// ResolvePath returns an absolute path for `p`, optionally relative to `from`. The returned path is
// cleaned by `filepath.Clean()` (so `..`, etc are removed). If `from` is empty, `p` is returned as a relative path.
func ResolvePath(from, p string) string {
	if from == "" {
		return filepath.Clean(p)
	} else if filepath.IsAbs(p) {
		return filepath.Clean(p)
	} else {
		dir := filepath.Dir(from)
		return filepath.Join(dir, p) // Join also does a Clean
	}
}

////////////////////////////////////////////////////////////////////////////////
//
// VNSImporter: A jsonnet.Importer for a vfs.NameSpace.
//

// VNSImporter implements the `jsonnet.Importer` interface but for the `vfs.NameSpace`. It is a wrapper
// around the `VFSImporter` but allows for multiple filesystems to be mounted at different paths.
type VNSImporter struct {
	*VFSImporter
	ns vfs.NameSpace
	lk sync.Mutex
}

func NewVNSImporter(ns vfs.NameSpace) *VNSImporter {
	if ns == nil {
		ns = vfs.NewNameSpace()
	}
	return &VNSImporter{
		VFSImporter: NewVFSImporter(ns),
		ns:          ns,
	}
}

func (i *VNSImporter) Bind(mountpoint string, fs vfs.FileSystem) {
	i.lk.Lock()
	defer i.lk.Unlock()
	i.ns.Bind(mountpoint, fs, "/", vfs.BindBefore)
}

func (i *VNSImporter) BindFS(mountpoint string, fs fs.FS) {
	i.Bind(mountpoint, vfs.FromFS(fs))
}
