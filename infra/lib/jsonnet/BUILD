load("//tools/bzl:go.bzl", "go_library", "go_test")

package(default_visibility = ["//infra:internal"])

go_library(
    name = "jsonnet",
    srcs = [
        "embedfs.go",
        "vfs.go",
        "vm.go",
    ],
    importpath = "github.com/augmentcode/augment/infra/lib/jsonnet",
    deps = [
        "//infra/lib/k8s",
        "@google_jsonnet_go//:go_default_library",
        "@org_golang_x_tools//godoc/vfs",
    ],
)

go_test(
    name = "jsonnet_test",
    srcs = [
        "embedfs_test.go",
        "vfs_test.go",
        "vm_test.go",
    ],
    embed = [":jsonnet"],
    embedsrcs = [
        "embedfs_test.jsonnet",
    ],
    deps = [
        "@com_github_google_go_cmp//cmp",
        "@google_jsonnet_go//:go_default_library",
        "@org_golang_x_tools//godoc/vfs/mapfs",
    ],
)
