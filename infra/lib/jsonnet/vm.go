package jsonnet

import (
	"encoding/json"
	"fmt"
	"strings"
	"sync"

	"github.com/google/go-jsonnet"

	"github.com/augmentcode/augment/infra/lib/k8s"
)

// VM is a wrapper around a raw Jsonnet VM. It provides a simplified interface for evaluating with TLA, both from files or snippets, and returning
// structured JSON or `k8s.Objects`. It may also be used to provide some additional native functions.
type VM struct {
	vm    *jsonnet.VM
	tlaLk sync.Mutex
}

func NewVM(vm *jsonnet.VM, i jsonnet.Importer) *VM {
	if vm == nil {
		vm = jsonnet.MakeVM()
	}
	if i != nil {
		vm.Importer(i)
	}
	return &VM{
		vm: vm,
	}
}

func (vm *VM) evalSnippet(fname, snippet string) (string, error) {
	buf, err := vm.vm.EvaluateAnonymousSnippet(fname, snippet)
	if err != nil {
		lines := []string{}
		for i, snipline := range strings.Split(snippet, "\n") {
			lines = append(lines, fmt.Sprintf("\t%4d: %s", i+1, snipline))
		}
		return "", fmt.Errorf("Input Snippet:\n%s\n%v", strings.Join(lines, "\n"), err)
	}
	return buf, nil
}

// Eval evaluates a json buffer (string) from either a file or a snippet. When `snippet` is
// empty, `fname` is evaluated with the given TLA code args. When `snippet` is not empty,
// it is evaluated with the given TLA coe args and `fname` is only used for formatting errors.
func (vm *VM) Eval(fname string, tla map[string]string, snippet string) (string, error) {
	vm.tlaLk.Lock()
	defer vm.tlaLk.Unlock()
	vm.vm.TLAReset()
	for k, v := range tla {
		vm.vm.TLACode(k, v)
	}

	if snippet == "" {
		return vm.vm.EvaluateFile(fname)
	} else {
		return vm.evalSnippet(fname, snippet)
	}
}

// EvalJSON wraps `Eval()` and calls `json.Unmarshal()`.
func (vm *VM) EvalJSON(fname string, tla map[string]string, snippet string, out any) error {
	buf, err := vm.Eval(fname, tla, snippet)
	if err != nil {
		return err
	}
	return json.Unmarshal([]byte(buf), out)
}

func (vm *VM) EvalK8s(fname string, tla map[string]string, snippet string) ([]k8s.Object, error) {
	buf, err := vm.Eval(fname, tla, snippet)
	if err != nil {
		return nil, err
	}
	return k8s.FromJSON([]byte(buf))
}
