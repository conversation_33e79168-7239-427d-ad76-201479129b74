package jsonnet

import (
	"embed"
	"strings"
	"testing"
)

//go:embed embedfs_test.jsonnet
var testdata embed.FS

func TestGlobalEmbedVM(t *testing.T) {
	t.<PERSON>lle<PERSON>()

	GlobalEmbedBindFS("/root0", testdata)
	vm := GlobalEmbedVM()

	got, err := vm.Eval("//root0/embedfs_test.jsonnet", nil, "")
	if err != nil {
		t.Fatal(err)
	}
	if got, want := "42", strings.TrimSpace(got); got != want {
		t.Errorf("got %v, want %v.", got, want)
	}
}
