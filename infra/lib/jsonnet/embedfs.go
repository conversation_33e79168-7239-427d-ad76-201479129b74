package jsonnet

import (
	"io/fs"

	"github.com/google/go-jsonnet"
)

var gEmbedImporter = NewVNSImporter(nil)

// GlobalEmbedImporter returns a global jsonnet.Importer which can be populated by `GlobalEmbedBindFS()` calls during `init()` throughout
// the build system. The main use case for this is for collecting jsonnet files from around the build system which would otherwise be difficult because
// go:embed only supports embedding at or below the current directory.
func GlobalEmbedImporter() jsonnet.Importer {
	return gEmbedImporter
}

func GlobalEmbedBindFS(mountpoint string, fs fs.FS) {
	gEmbedImporter.BindFS(mountpoint, fs)
}

// GlobalEmbedVM returns a *new* VM but which uses the shared `GlobalEmbedImporter()`.
func GlobalEmbedVM() *VM {
	return NewVM(nil, GlobalEmbedImporter())
}
