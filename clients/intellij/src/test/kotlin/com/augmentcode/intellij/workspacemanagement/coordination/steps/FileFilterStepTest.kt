package com.augmentcode.intellij.workspacemanagement.coordination.steps

import com.augmentcode.api.FeatureFlags
import com.augmentcode.api.Language
import com.augmentcode.intellij.index.ignore.PathFilterService
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.pluginstate.PluginStateService
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.waitForAssertion
import com.augmentcode.intellij.workspacemanagement.utils.RoughlySizedChannel
import com.intellij.openapi.fileTypes.PlainTextFileType
import com.intellij.openapi.util.Disposer
import com.intellij.openapi.util.io.FileUtil
import com.intellij.openapi.vfs.LocalFileSystem
import com.intellij.openapi.vfs.VFileProperty
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.testFramework.PsiTestUtil
import com.intellij.testFramework.registerOrReplaceServiceInstance
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.test.runTest
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import java.io.File
import java.nio.file.Path
import java.nio.file.Paths

@OptIn(ExperimentalCoroutinesApi::class)
@RunWith(JUnit4::class)
class FileFilterStepTest : AugmentBasePlatformTestCase() {
  private lateinit var inputChannel: RoughlySizedChannel<Path>
  private lateinit var outputChannel: RoughlySizedChannel<VirtualFile>
  private lateinit var fileFilterStep: FileFilterStep
  private lateinit var tempDir: File

  override fun getTestDataPath() = "src/test/testData/workspacemanagement/files-for-filtering"

  override fun setUp() {
    super.setUp()
    tempDir = FileUtil.createTempDirectory("test", null)
    inputChannel = RoughlySizedChannel(Channel(Channel.UNLIMITED))
    outputChannel = RoughlySizedChannel(Channel(Channel.UNLIMITED))
    fileFilterStep =
      FileFilterStep(
        project,
        augmentHelpers().createCoroutineScope(Dispatchers.IO),
        inputChannel,
        outputChannel,
      )
  }

  override fun tearDown() {
    Disposer.dispose(fileFilterStep)
    FileUtil.delete(tempDir)
    super.tearDown()
  }

  @Test
  fun testStartProcessing_startsJobOnlyOnce() {
    assertNull(fileFilterStep.processingJob)
    fileFilterStep.startProcessing()
    assertNotNull(fileFilterStep.processingJob)
    val firstJob = fileFilterStep.processingJob

    // Second call should be ignored
    fileFilterStep.startProcessing()
    assertEquals(firstJob, fileFilterStep.processingJob)
  }

  @Test
  fun testStopProcessing_stopsJob() {
    assertNull(fileFilterStep.processingJob)
    fileFilterStep.startProcessing()
    assertNotNull(fileFilterStep.processingJob)

    // Stop job
    Disposer.dispose(fileFilterStep)
    assertNull(fileFilterStep.processingJob)
  }

  @Test
  fun testProcessFilesFromChannel_handlesCommonCases() =
    runTest {
      augmentHelpers().forcePluginState(PluginState.ENABLED)

      val nonExistentPath = Paths.get("/non/existent/file.txt")
      inputChannel.send(nonExistentPath)

      val files =
        createLocalFiles(
          mutableListOf(Pair("test.txt", "test content")),
        )
      val testPath = Paths.get(files["test.txt"]!!.path)
      inputChannel.send(testPath)

      fileFilterStep.startProcessing()

      // Wait for item to be processed
      waitForAssertion({
        assertTrue(inputChannel.isEmpty)
      })

      val result = outputChannel.receive()
      assertEquals("Should have received correct file", files["test.txt"]!!.path, result.path)
    }

  @Test
  fun testProcessFilesFromChannel_rejectsFileWhenNotSignedIn() =
    runTest {
      augmentHelpers().forcePluginState(PluginState.SIGN_IN_REQUIRED)

      // Create a test file
      val files =
        createLocalFiles(
          mutableListOf(Pair("test.txt", "test content")),
        )
      val testFile = files["test.txt"]!!
      val testPath = Paths.get(testFile.path)
      inputChannel.send(testPath)

      fileFilterStep.startProcessing()

      waitForAssertion({
        assertTrue(inputChannel.isEmpty)
      })

      // Then - no file should be sent to output channel
      assertTrue("Output channel should be empty when not signed in", outputChannel.isEmpty)
    }

  @Test
  fun testIsSupportedFileExtension_withBypassLanguageFilter() =
    runTest {
      val apiModel =
        augmentHelpers().createModelConfig(
          FeatureFlags().apply {
            bypassLanguageFilter = true
          },
        )
      augmentHelpers().forcePluginState(PluginState.ENABLED, apiModel)

      val files =
        createLocalFiles(
          mutableListOf(
            Pair("no-extension", "test content"),
            Pair("test.txt", "test content"),
            Pair("test.unknown", "test content"),
          ),
        )
      val noExtFile = files["no-extension"]!!
      val noExtPath = Paths.get(noExtFile.path)
      inputChannel.send(noExtPath)

      val txtFile = files["test.txt"]!!
      val txtExt = Paths.get(txtFile.path)
      inputChannel.send(txtExt)

      val unknownFile = files["test.unknown"]!!
      val unknownExt = Paths.get(unknownFile.path)
      inputChannel.send(unknownExt)

      fileFilterStep.startProcessing()

      waitForAssertion({
        assertTrue(inputChannel.isEmpty)
      })

      val outputFiles = waitForFiles(3, outputChannel)
      assertTrue(outputChannel.isEmpty)

      assertEquals(outputFiles[0].path, noExtFile.path)
      assertEquals(outputFiles[1].path, txtFile.path)
      assertEquals(outputFiles[2].path, unknownFile.path)
    }

  @Test
  fun testIsSupportedFileExtension_simpleWithBypassLanguageFilter() =
    runTest {
      val apiModel =
        augmentHelpers().createModelConfig(
          FeatureFlags().apply {
            bypassLanguageFilter = true
          },
        )
      augmentHelpers().forcePluginState(PluginState.ENABLED, apiModel)

      val flags = PluginStateService.instance.context.flags
      val model = PluginStateService.instance.context.model!!
      assertTrue(
        "Should accept any extension when bypass is enabled",
        fileFilterStep.isSupportedFileExtension(flags, model, "unknown"),
      )
      assertTrue(
        "Should accept null extension when bypass is enabled",
        fileFilterStep.isSupportedFileExtension(flags, model, null),
      )
    }

  @Test
  fun testIsSupportedFileExtension_languageFiltering() =
    runTest {
      val apiModel =
        augmentHelpers().createModelConfig(
          FeatureFlags().apply {
            bypassLanguageFilter = false
          },
        )
      apiModel.supportedLanguages =
        listOf(
          Language().apply {
            name = "Text"
            extensions = setOf(".txt")
          },
        )
      augmentHelpers().forcePluginState(PluginState.ENABLED, apiModel)

      val files =
        createLocalFiles(
          mutableListOf(
            Pair("no-extension", "test content"),
            Pair("test.txt", "test content"),
            Pair("test.unknown", "test content"),
          ),
        )
      val noExtFile = files["no-extension"]!!
      val noExtPath = Paths.get(noExtFile.path)
      inputChannel.send(noExtPath)

      val txtFile = files["test.txt"]!!
      val txtExt = Paths.get(txtFile.path)
      inputChannel.send(txtExt)

      val unknownFile = files["test.unknown"]!!
      val unknownExt = Paths.get(unknownFile.path)
      inputChannel.send(unknownExt)

      fileFilterStep.startProcessing()

      waitForAssertion({
        assertTrue(inputChannel.isEmpty)
      })

      val outputFiles = waitForFiles(1, outputChannel)
      assertTrue(outputChannel.isEmpty)

      assertEquals(outputFiles[0].path, txtFile.path)
    }

  @Test
  fun testIsSupportedFileExtension_simpleExtensionFiltering() {
    val apiModel =
      augmentHelpers().createModelConfig(
        FeatureFlags().apply {
          bypassLanguageFilter = false
        },
      )
    apiModel.supportedLanguages =
      listOf(
        Language().apply {
          name = "Text"
          extensions = setOf(".txt")
        },
        Language().apply {
          name = "Kotlin"
          extensions = setOf(".kt")
        },
      )
    augmentHelpers().forcePluginState(PluginState.ENABLED, apiModel)

    val flags = PluginStateService.instance.context.flags
    val model = PluginStateService.instance.context.model!!

    assertTrue(
      "Should accept supported extension",
      fileFilterStep.isSupportedFileExtension(flags, model, "txt"),
    )
    assertTrue(
      "Should accept supported extension",
      fileFilterStep.isSupportedFileExtension(flags, model, "kt"),
    )
    assertFalse(
      "Should reject unsupported extension",
      fileFilterStep.isSupportedFileExtension(flags, model, "unknown"),
    )
    assertFalse(
      "Should reject null extension",
      fileFilterStep.isSupportedFileExtension(flags, model, null),
    )
  }

  @Test
  fun testIsSupportedFileExtension_binaryFileFiltering() =
    runTest {
      augmentHelpers().forcePluginState(
        PluginState.ENABLED,
        augmentHelpers().createModelConfig(
          FeatureFlags().apply {
            bypassLanguageFilter = false
          },
        ),
      )

      val files =
        copyTestDataFiles(
          listOf(
            "auggie.png",
            "example.txt",
          ),
        )
      val pngFile = files["auggie.png"]!!
      val pngPath = Paths.get(pngFile.path)
      inputChannel.send(pngPath)

      val txtFile = files["example.txt"]!!
      val txtPath = Paths.get(txtFile.path)
      inputChannel.send(txtPath)

      fileFilterStep.startProcessing()

      waitForAssertion({
        assertTrue(inputChannel.isEmpty)
      })

      val outputFiles = waitForFiles(1, outputChannel)
      assertTrue(outputChannel.isEmpty)

      assertEquals(outputFiles[0].path, txtFile.path)
    }

  @Test
  fun testIsSupportedFileExtension_fileSizeFiltering() =
    runTest {
      augmentHelpers().forcePluginState(
        PluginState.ENABLED,
        augmentHelpers().createModelConfig(
          FeatureFlags().apply {
            maxUploadSizeBytes = 100
          },
        ),
      )

      val files =
        copyTestDataFiles(
          listOf(
            "100-bytes.txt",
            "example.txt",
          ),
        )
      val largeFile = files["100-bytes.txt"]!!
      val largeFilePath = Paths.get(largeFile.path)
      inputChannel.send(largeFilePath)

      val txtFile = files["example.txt"]!!
      val txtPath = Paths.get(txtFile.path)
      inputChannel.send(txtPath)

      fileFilterStep.startProcessing()

      waitForAssertion({
        assertTrue(inputChannel.isEmpty)
      })

      val outputFiles = waitForFiles(1, outputChannel)
      assertTrue(outputChannel.isEmpty)
      assertEquals(outputFiles[0].path, txtFile.path)
    }

  @Test
  fun testProcessFilesFromChannel_rejectsSymlink() =
    runTest {
      augmentHelpers().forcePluginState(PluginState.ENABLED)

      // Create a test file
      val testFile = myFixture.createFile("test.txt", "test content")

      val diffCanonicalPath = mockk<VirtualFile>(relaxed = true)
      every { diffCanonicalPath.path } returns testFile.path
      every { diffCanonicalPath.canonicalPath } returns "/different/path/test.txt" // Different from path
      every { diffCanonicalPath.fileType } returns PlainTextFileType.INSTANCE
      every { diffCanonicalPath.`is`(any()) } returns false
      every { diffCanonicalPath.length } returns 100L
      every { diffCanonicalPath.extension } returns "txt"

      val symlinkProperty = mockk<VirtualFile>(relaxed = true)
      every { symlinkProperty.path } returns testFile.path
      every { symlinkProperty.canonicalPath } returns testFile.path
      every { symlinkProperty.fileType } returns PlainTextFileType.INSTANCE
      every { symlinkProperty.`is`(VFileProperty.SYMLINK) } returns true
      every { symlinkProperty.length } returns 100L
      every { symlinkProperty.extension } returns "txt"

      assertFalse(
        fileFilterStep.isAccepted(
          PluginStateService.instance.context.flags,
          PluginStateService.instance.context.model!!,
          diffCanonicalPath,
        ),
      )
      assertFalse(
        fileFilterStep.isAccepted(PluginStateService.instance.context.flags, PluginStateService.instance.context.model!!, symlinkProperty),
      )
    }

  @Test
  fun testProcessFilesFromChannel_rejectsFileByGitignore() =
    runTest {
      augmentHelpers().forcePluginState(PluginState.ENABLED)
      val mockPathFilterService = mockk<PathFilterService>(relaxed = true)
      coEvery { mockPathFilterService.isAccepted(any<VirtualFile>()) } answers {
        firstArg<VirtualFile>().name == "example.txt"
      }
      project.registerOrReplaceServiceInstance(
        PathFilterService::class.java,
        mockPathFilterService,
        testRootDisposable,
      )

      val files =
        copyTestDataFiles(
          listOf(
            "100-bytes.txt",
            "example.txt",
          ),
        )
      val largeFile = files["100-bytes.txt"]!!
      val largeFilePath = Paths.get(largeFile.path)
      inputChannel.send(largeFilePath)

      val txtFile = files["example.txt"]!!
      val txtPath = Paths.get(txtFile.path)
      inputChannel.send(txtPath)

      fileFilterStep.startProcessing()

      waitForAssertion({
        assertTrue(inputChannel.isEmpty)
      })

      val outputFiles = waitForFiles(1, outputChannel)
      assertTrue(outputChannel.isEmpty)

      assertEquals(outputFiles[0].path, txtFile.path)
    }

  fun createLocalFiles(files: List<Pair<String, String>>): MutableMap<String, File> {
    val actualFiles = mutableMapOf<String, File>()
    files.forEach {
      val file = tempDir.resolve(it.first)
      file.writeText(it.second)
      actualFiles[it.first] = file
    }
    // Since we expect the files to be on disk, we need to set a content root
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)
    return actualFiles
  }

  fun copyTestDataFiles(files: List<String>): MutableMap<String, File> {
    val testDataPath = myFixture.testDataPath

    val actualFiles = mutableMapOf<String, File>()
    files.forEach {
      val sourceFile = File(testDataPath, it)
      val targetFile = File(tempDir, it) // where you want to copy it
      sourceFile.copyTo(targetFile, overwrite = true)
      actualFiles[it] = targetFile
    }

    // Since we expect the files to be on disk, we need to set a content root
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)
    return actualFiles
  }

  suspend fun waitForFiles(
    expectedCount: Int,
    channel: RoughlySizedChannel<VirtualFile>,
  ): List<VirtualFile> {
    val outputFiles = mutableListOf<VirtualFile>()
    // Because we use runTest, need to wrap with withContext to use "real" time
    // The withTimeout is only needed in the event that the channel never received a file when we expect it.
    withContext(Dispatchers.Default.limitedParallelism(1)) {
      withTimeout(2000) {
        while (outputFiles.size < expectedCount) {
          outputFiles.add(channel.receive())
        }
      }
    }
    return outputFiles
  }
}
