package com.augmentcode.intellij.workspacemanagement.coordination

import kotlinx.coroutines.delay
import kotlin.time.Duration

class RateLimitedFunction<A, R>(
  private val delegate: suspend (A) -> R,
  private val backoffDuration: suspend (R) -> Duration = { Duration.ZERO },
) {
  private var nextInvocationTimestamp = 0L

  suspend fun invoke(request: A): R {
    val now = System.currentTimeMillis()
    if (nextInvocationTimestamp > now) {
      delay(nextInvocationTimestamp - now)
    }
    val result = delegate(request)
    nextInvocationTimestamp = System.currentTimeMillis() + backoffDuration(result).inWholeMilliseconds
    return result
  }
}
