package com.augmentcode.intellij.workspacemanagement.coordination.steps

import com.augmentcode.api.BatchUploadRequest
import com.augmentcode.api.UploadableBlob
import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.index.AugmentBlobState
import com.augmentcode.intellij.workspacemanagement.utils.RoughlySizedChannel
import com.intellij.openapi.Disposable
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.fileEditor.impl.LoadTextUtil
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.vfs.readBytes
import kotlinx.coroutines.*
import java.nio.file.NoSuchFileException as JavaNoSuchFileException
import kotlin.io.NoSuchFileException as KotlinNoSuchFileException

class BatchFileUploader(
  private val scope: CoroutineScope,
  private val inputChannel: RoughlySizedChannel<FileToUpload>,
  private val outputChannel: RoughlySizedChannel<AugmentBlobState>,
) : Disposable {
  private var uploadJob: Job? = null
  private var lastUploadTime = 0L // ms

  private val logger = thisLogger()

  companion object {
    const val MAX_BATCH_SIZE = 1000
    const val MAX_BATCH_CONTENT_SIZE_BYTES = 250_000
    const val UPLOAD_CADENCE_MS = 1000L // TODO(diehuxx): is this what repo scale team wants?
  }

  override fun dispose() {
    stopUpload()
  }

  fun startUpload() {
    if (uploadJob != null) {
      logger.info("Upload job already running")
      return
    }

    uploadJob =
      scope.launch {
        logger.info("Starting upload job")
        while (isActive) {
          try {
            processUploadBatch()
          } catch (e: CancellationException) {
            throw e
          } catch (e: Exception) {
            logger.warn("Failed to process upload batch", e)
          }
        }
      }
  }

  fun stopUpload() {
    thisLogger().info("Stopping upload job")
    uploadJob?.cancel()
    uploadJob = null
  }

  private suspend fun processUploadBatch() {
    var contentLength = 0L
    val batch = mutableListOf<FileToUpload>()

    // Suspend while we wait for the first item
    val firstItem = inputChannel.receive()
    batch.add(firstItem)
    contentLength += firstItem.virtualFile.length

    // Collect batch without blocking
    while (batch.size < MAX_BATCH_SIZE) {
      val item = inputChannel.tryReceive().getOrNull() ?: break
      if (contentLength + item.virtualFile.length > MAX_BATCH_CONTENT_SIZE_BYTES) {
        // Put the request back in the queue for the next batch
        val result = inputChannel.trySend(item)
        if (result.isFailure) {
          logger.warn("Failed to requeue item for retry", result.exceptionOrNull())
        }
        break
      }

      logger.debug("Adding ${item.relPath} to batch")
      batch.add(item)
      contentLength += item.virtualFile.length
    }

    logger.info("Uploading ${batch.size} blobs with a total content length of $contentLength")
    val result = uploadBatch(batch)
    logger.info(
      "Uploaded ${result.successfulUploads.size} blobs successfully, " +
        "${result.failedUploads.size} failed," +
        "${result.unreadableFiles.size} unreadable",
    )
    result.successfulUploads.forEach {
      sendToOutput(it)
    }
    result.failedUploads.forEach {
      requeueForRetry(it)
    }
    // unreadableFiles are not retryable, so we just log and move on
  }

  private suspend fun sendToOutput(successfulUpload: Pair<FileToUpload, String>) {
    val (fileToUpload, actualBlobName) = successfulUpload
    outputChannel.send(
      AugmentBlobState(
        fileToUpload.relPath,
        actualBlobName, // remoteName - use actual returned blob name
        fileToUpload.expectedBlobName, // localName - keep expected
        fileToUpload.rootPath,
        System.currentTimeMillis(),
      ),
    )
  }

  private suspend fun requeueForRetry(failedUpload: FileToUpload) {
    if (!failedUpload.canRetry()) {
      logger.info(
        "Failed to upload ${failedUpload.relPath} with expected blob name ${failedUpload.expectedBlobName} " +
          "after ${failedUpload.retryCount} retries. Giving up.",
      )
      return
    }
    failedUpload.incrementRetryCount()
    inputChannel.send(failedUpload)
  }

  data class UploadBatchResult(
    val successfulUploads: Set<Pair<FileToUpload, String>>,
    val failedUploads: Set<FileToUpload>,
    val unreadableFiles: Set<FileToUpload>,
  )

  /**
   * returns a list of blob names of the successfully uploaded blobs
   */
  suspend fun uploadBatch(filesToUpload: List<FileToUpload>): UploadBatchResult {
    // Filter out unreadable files and build the list of blobs to upload
    // It is important that we only call uploadableBlob once per request, as it may perform I/O
    val unreadableFiles = mutableSetOf<FileToUpload>()
    val blobs =
      filesToUpload.mapNotNull {
        val uploadableBlob = it.expensivelyGenerateUploadableBlob()
        if (uploadableBlob == null) {
          unreadableFiles.add(it)
        }
        uploadableBlob
      }

    // Enforce upload cadence so we don't overload the server
    if (System.currentTimeMillis() - lastUploadTime < UPLOAD_CADENCE_MS) {
      delay(System.currentTimeMillis() - lastUploadTime)
    }

    val uploadResponse =
      try {
        AugmentAPI.instance.batchUpload(
          BatchUploadRequest().apply {
            this.blobs = blobs
          },
        )
      } catch (e: CancellationException) {
        throw e
      } catch (e: Exception) {
        logger.warn("Failed to upload ${filesToUpload.size} blobs", e)
        return UploadBatchResult(
          successfulUploads = emptySet(),
          failedUploads = filesToUpload.toSet(),
          unreadableFiles,
        )
      } finally {
        lastUploadTime = System.currentTimeMillis()
      }

    if (uploadResponse == null) {
      logger.warn("Failed to upload ${filesToUpload.size} blobs. Response is null")
      return UploadBatchResult(
        successfulUploads = emptySet(),
        failedUploads = filesToUpload.toSet(),
        unreadableFiles,
      )
    }

    if (uploadResponse.blobNames.size != filesToUpload.size) {
      logger.warn("Expected ${filesToUpload.size} blob names while batch uploading but got ${uploadResponse.blobNames.size}")
    }

    val successfulUploads = mutableSetOf<Pair<FileToUpload, String>>()
    val failedUploads = mutableSetOf<FileToUpload>()

    for (i in 0 until minOf(uploadResponse.blobNames.size, filesToUpload.size)) {
      val fileToUpload = filesToUpload[i]
      val returnedBlobName = uploadResponse.blobNames[i]

      if (returnedBlobName != fileToUpload.expectedBlobName) {
        logger.warn("Upload blob name mismatch: ${fileToUpload.expectedBlobName} -> $returnedBlobName")
      }

      successfulUploads.add(fileToUpload to returnedBlobName)
    }

    // Any files beyond the response length are considered failed
    for (i in uploadResponse.blobNames.size until filesToUpload.size) {
      failedUploads.add(filesToUpload[i])
    }

    return UploadBatchResult(
      successfulUploads = successfulUploads,
      failedUploads = failedUploads,
      unreadableFiles,
    )
  }
}

data class FileToUpload(
  val rootPath: String,
  val relPath: String,
  val expectedBlobName: String,
  val virtualFile: VirtualFile,
  var retryCount: Int = 0,
) {
  companion object {
    const val MAX_RETRY_COUNT = 3
  }

  /**
   * Generates the UploadableBlob for the API request.
   * This brings the entire file contents into memory, which can be expensive.
   * It is important that you only call it exactly once per blob immediately
   * before uploading.
   * returns the uploadable blob, or null if the file is no longer readable
   */
  fun expensivelyGenerateUploadableBlob(): UploadableBlob? {
    try {
      // This API is used under the hood in FileContent#contentAsText, and it does not require read lock
      // https://github.com/JetBrains/intellij-community/blob/e4bea1954dcf234bbcc62e81bf01a9f424aad2d2/platform/core-impl/src/com/intellij/util/indexing/FileContentImpl.java#L184-L186
      val normalizedText =
        LoadTextUtil.getTextByBinaryPresentation(virtualFile.readBytes(), virtualFile, false, false).toString()
      return UploadableBlob().apply {
        path = relPath
        root = rootPath
        content = normalizedText
      }
    } catch (_: KotlinNoSuchFileException) {
      // In case of race condition where the file was deleted after it was enqueued for upload
      return null
    } catch (_: JavaNoSuchFileException) {
      // Also handle Java's NoSuchFileException
      return null
    }
  }

  fun incrementRetryCount() {
    retryCount++
  }

  fun canRetry() = retryCount < MAX_RETRY_COUNT
}
