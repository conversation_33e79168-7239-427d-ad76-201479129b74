import { describe, it, expect } from "vitest";
import { calculateImageStats } from "./image-utils";
import type { JSONContent } from "@tiptap/core";

describe("image integration", () => {
  const MAX_IMAGES = 10;
  const MAX_IMAGE_SIZE_KB = 1024;

  it("should correctly identify when image limits are exceeded", () => {
    // Test case 1: Too many images
    const tooManyImagesContent: JSONContent = {
      type: "doc",
      content: Array.from({ length: 12 }, (_, i) => ({
        type: "paragraph",
        content: [
          {
            type: "image",
            attrs: {
              src: `image${i}.jpg`,
              title: `Image ${i}`,
              fileSizeBytes: 500 * 1024, // 500KB each
              isLoading: false,
            },
          },
        ],
      })),
    };

    const tooManyStats = calculateImageStats(tooManyImagesContent);
    expect(tooManyStats.imageCount).toBe(12);
    expect(tooManyStats.imageCount > MAX_IMAGES).toBe(true);

    // Test case 2: Total size too large
    const tooLargeSizeContent: JSONContent = {
      type: "doc",
      content: [
        {
          type: "paragraph",
          content: [
            {
              type: "image",
              attrs: {
                src: "large-image.jpg",
                title: "Large Image",
                fileSizeBytes: 2048 * 1024, // 2MB
                isLoading: false,
              },
            },
          ],
        },
      ],
    };

    const tooLargeStats = calculateImageStats(tooLargeSizeContent);
    expect(tooLargeStats.imageSizeSumKb).toBe(2048);
    expect(tooLargeStats.imageSizeSumKb > MAX_IMAGE_SIZE_KB).toBe(true);

    // Test case 3: Within limits
    const withinLimitsContent: JSONContent = {
      type: "doc",
      content: [
        {
          type: "paragraph",
          content: [
            {
              type: "image",
              attrs: {
                src: "small-image.jpg",
                title: "Small Image",
                fileSizeBytes: 100 * 1024, // 100KB
                isLoading: false,
              },
            },
          ],
        },
      ],
    };

    const withinLimitsStats = calculateImageStats(withinLimitsContent);
    expect(withinLimitsStats.imageCount).toBe(1);
    expect(withinLimitsStats.imageSizeSumKb).toBe(100);
    expect(withinLimitsStats.imageCount <= MAX_IMAGES).toBe(true);
    expect(withinLimitsStats.imageSizeSumKb <= MAX_IMAGE_SIZE_KB).toBe(true);
  });

  it("should handle mixed loading and loaded images correctly", () => {
    const mixedContent: JSONContent = {
      type: "doc",
      content: [
        {
          type: "paragraph",
          content: [
            {
              type: "image",
              attrs: {
                src: "loaded-image.jpg",
                title: "Loaded Image",
                fileSizeBytes: 500 * 1024, // 500KB
                isLoading: false,
              },
            },
            {
              type: "image",
              attrs: {
                src: "loading-image.jpg",
                title: "Loading Image",
                fileSizeBytes: 1000 * 1024, // 1MB (should be ignored)
                isLoading: true,
              },
            },
          ],
        },
      ],
    };

    const stats = calculateImageStats(mixedContent);
    // Only the loaded image should be counted
    expect(stats.imageCount).toBe(1);
    expect(stats.imageSizeSumKb).toBe(500);
  });
});
