import { describe, it, expect } from "vitest";
import { extractImagesFromContent, calculateImageStats } from "./image-utils";
import type { JSONContent } from "@tiptap/core";

describe("image-utils", () => {
  describe("extractImagesFromContent", () => {
    it("should return empty array for null/undefined content", () => {
      expect(extractImagesFromContent(null)).toEqual([]);
      expect(extractImagesFromContent(undefined)).toEqual([]);
    });

    it("should extract image information from rich text content", () => {
      const content: JSONContent = {
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [
              {
                type: "image",
                attrs: {
                  src: "image1.jpg",
                  title: "Test Image 1",
                  fileSizeBytes: 1024,
                  isLoading: false,
                },
              },
            ],
          },
          {
            type: "paragraph",
            content: [
              {
                type: "image",
                attrs: {
                  src: "image2.png",
                  title: "Test Image 2",
                  fileSizeBytes: 2048,
                  isLoading: true,
                },
              },
            ],
          },
        ],
      };

      const images = extractImagesFromContent(content);
      expect(images).toHaveLength(2);
      expect(images[0]).toEqual({
        src: "image1.jpg",
        title: "Test Image 1",
        fileSizeBytes: 1024,
        isLoading: false,
      });
      expect(images[1]).toEqual({
        src: "image2.png",
        title: "Test Image 2",
        fileSizeBytes: 2048,
        isLoading: true,
      });
    });

    it("should handle content without images", () => {
      const content: JSONContent = {
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [
              {
                type: "text",
                text: "Just some text",
              },
            ],
          },
        ],
      };

      const images = extractImagesFromContent(content);
      expect(images).toEqual([]);
    });
  });

  describe("calculateImageStats", () => {
    it("should return zero stats for null/undefined content", () => {
      expect(calculateImageStats(null)).toEqual({
        imageCount: 0,
        imageSizeSumKb: 0,
      });
      expect(calculateImageStats(undefined)).toEqual({
        imageCount: 0,
        imageSizeSumKb: 0,
      });
    });

    it("should calculate correct stats excluding loading images", () => {
      const content: JSONContent = {
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [
              {
                type: "image",
                attrs: {
                  src: "image1.jpg",
                  title: "Test Image 1",
                  fileSizeBytes: 1024, // 1KB
                  isLoading: false,
                },
              },
              {
                type: "image",
                attrs: {
                  src: "image2.png",
                  title: "Test Image 2",
                  fileSizeBytes: 2048, // 2KB
                  isLoading: true, // This should be excluded
                },
              },
              {
                type: "image",
                attrs: {
                  src: "image3.gif",
                  title: "Test Image 3",
                  fileSizeBytes: 3072, // 3KB
                  isLoading: false,
                },
              },
            ],
          },
        ],
      };

      const stats = calculateImageStats(content);
      expect(stats).toEqual({
        imageCount: 2, // Only non-loading images
        imageSizeSumKb: 4, // (1024 + 3072) / 1024 = 4KB
      });
    });

    it("should handle content without images", () => {
      const content: JSONContent = {
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [
              {
                type: "text",
                text: "Just some text",
              },
            ],
          },
        ],
      };

      const stats = calculateImageStats(content);
      expect(stats).toEqual({
        imageCount: 0,
        imageSizeSumKb: 0,
      });
    });
  });
});
