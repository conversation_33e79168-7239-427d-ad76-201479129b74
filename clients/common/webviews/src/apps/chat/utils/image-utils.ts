import type { JSONContent } from "@tiptap/core";

export const MAX_IMAGES_COUNT = 10;
export const MAX_IMAGE_SIZE_KB = 1024;

export interface ImageInfo {
  src: string;
  title: string;
  fileSizeBytes: number;
  isLoading: boolean;
}

/**
 * Extract image information from rich text JSON content
 * @param json The rich text JSON content
 * @returns Array of image information
 */
export function extractImagesFromContent(json: JSONContent | null | undefined): ImageInfo[] {
  if (!json) {
    return [];
  }

  const images: ImageInfo[] = [];

  const traverse = (node: JSONContent) => {
    if (!node) {
      return;
    }

    if (node.type === "image" && node.attrs) {
      images.push({
        src: node.attrs.src || "",
        title: node.attrs.title || "",
        fileSizeBytes: Number(node.attrs.fileSizeBytes) || 0,
        isLoading: Boolean(node.attrs.isLoading),
      });
    }

    if (node.content && Array.isArray(node.content)) {
      for (const contentNode of node.content) {
        traverse(contentNode);
      }
    }
  };

  traverse(json);
  return images;
}

/**
 * Calculate total image size in KB and count from rich text content
 * @param json The rich text JSON content
 * @returns Object with imageCount and imageSizeSumKb
 */
export function calculateImageStats(json: JSONContent | null | undefined): {
  imageCount: number;
  imageSizeSumKb: number;
} {
  const images = extractImagesFromContent(json);
  
  // Only count non-loading images for accurate stats
  const processedImages = images.filter(img => !img.isLoading);
  
  const imageCount = processedImages.length;
  const imageSizeSumKb = Math.round(
    processedImages.reduce((sum, img) => sum + img.fileSizeBytes, 0) / 1024
  );

  return {
    imageCount,
    imageSizeSumKb,
  };
}

