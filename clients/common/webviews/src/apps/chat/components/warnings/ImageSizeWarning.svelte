<script lang="ts">
  import CalloutAugment from "$common-webviews/src/design-system/components/CalloutAugment.svelte";
  import ExclamationTriangleIcon from "$common-webviews/src/design-system/icons/exclamation-triangle.svelte";

  export let imageCount;
  export let maxImages;
  export let imageSizeLimitKb;
  export let imageSizeSumKb;
</script>

{#if imageCount > maxImages || imageSizeSumKb > imageSizeLimitKb}
<div class="c-image-warning">
    <CalloutAugment variant="soft" color="warning" size={1}>
        <ExclamationTriangleIcon slot="icon" />
        {
            imageCount > maxImages
            ? `Up to ${maxImages} images are supported.`
            : `Total image size exceeds the ${imageSizeLimitKb}KB limit. Current size: ${imageSizeSumKb}KB`
        }
    </CalloutAugment>
</div>
{/if}
<style>
  .c-image-warning {
    position: relative;
  }
</style>