<script lang="ts">
  import BaseToolComponent from "../BaseToolComponent.svelte";
  import ToolUseHeader from "../ToolUseHeader.svelte";
  import RegularCloudIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/cloud-arrow-up.svg?component";

  import { getContext } from "svelte";
  import { ChatModeModel } from "$common-webviews/src/apps/chat/models/chat-mode-model";
  import {
    ToolUsePhase,
    type ToolUseState,
  } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import { stringOrDefault } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import OpenInNewWindow from "$common-webviews/src/design-system/icons/open-in-new-window.svelte";
  import SuccessfulButton from "$common-webviews/src/apps/chat/components/buttons/SuccessfulButton.svelte";

  export let toolUseInput: Record<string, unknown> = {};
  export let toolUseState: ToolUseState;

  // Get the chat mode model from context
  const chatModeModel = getContext<ChatModeModel>(ChatModeModel.key);

  // Extract tool arguments using reusable utility (like other tools)
  $: summary = stringOrDefault(toolUseInput.summary, "");

  // Format tool arguments for display - only show summary in collapsed state
  $: formattedArgs = (() => {
    const args: string[] = [];
    if (summary) args.push(summary);
    return args;
  })();

  // Extract remote agent ID from tool response
  $: remoteAgentId = (() => {
    if (
      toolUseState.phase === ToolUsePhase.completed &&
      toolUseState.result?.text &&
      !toolUseState.result.isError
    ) {
      try {
        const response = JSON.parse(toolUseState.result.text);
        // Only return the ID if the response indicates success
        if (response.success && (response.remoteAgentId || response.remote_agent_id)) {
          return response.remoteAgentId || response.remote_agent_id;
        }
      } catch (e) {
        console.error("Failed to parse spawn-sub-agent tool response:", e);
      }
    }
    return null;
  })();

  // Handle clicking on the button to switch to remote agent thread (following OpenFileButton pattern)
  const onGoToRemoteAgent = async function onGoToRemoteAgent(e: Event) {
    e?.stopPropagation?.();
    e?.preventDefault?.();
    if (!remoteAgentId || !chatModeModel) {
      return;
    }

    chatModeModel.switchToThread("remoteAgent", remoteAgentId);
    return "success";
  };

  // Determine if the button should be shown
  $: showButton = !!remoteAgentId;
</script>

<BaseToolComponent>
  <ToolUseHeader slot="header" toolName="Remote Agent" formattedToolArgs={formattedArgs}>
    <RegularCloudIcon slot="icon" />
    <span slot="toolAction">
      {#if showButton}
        <span class="c-go-to-remote-agent-button-container c-go-to-remote-agent-button__size--0">
          <SuccessfulButton
            defaultColor="neutral"
            stickyColor={false}
            size={0}
            variant="ghost-block"
            tooltip={{
              neutral: "Go to remote agent",
              success: "Opening remote agent...",
            }}
            stateVariant={{ success: "soft" }}
            onClick={onGoToRemoteAgent}
            icon={true}
          >
            <OpenInNewWindow slot="iconLeft" />
          </SuccessfulButton>
        </span>
      {/if}
    </span>
  </ToolUseHeader>
</BaseToolComponent>

<style>
  /* Button styling following OpenFileButton pattern */
  .c-go-to-remote-agent-button-container {
    display: contents;
  }

  .c-go-to-remote-agent-button__size--0 :global(.c-successful-button) {
    --base-btn-width: 16px;
    --base-btn-height: 16px;
    height: 16px;
    width: 16px;
  }
</style>
