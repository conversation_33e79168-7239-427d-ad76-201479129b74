import { truncate<PERSON><PERSON><PERSON>el<PERSON> } from "./strings";
import {
  TruncatedContentMetadata,
  TruncatedContentType,
  UntruncatedContentManager,
} from "./untruncated-content-manager";

/**
 * Options for truncating content
 */
export interface TruncateOptions {
  /** Maximum length in bytes */
  maxBytes: number;
  /** Type of content being truncated */
  contentType: TruncatedContentType;
  /** Tool use ID if applicable */
  toolUseId?: string;
  /** Request ID if applicable */
  requestId?: string;
  /** Conversation ID if applicable */
  conversationId?: string;
}

/**
 * Result of truncating content
 */
export interface TruncateResult {
  /** The truncated content */
  truncatedContent: string;
  /** Metadata about the truncation */
  metadata?: TruncatedContentMetadata;
}

/**
 * Truncates content in the middle and keep some metadata
 *
 * @param content The content to truncate
 * @param options Truncation options
 * @param contentManager The untruncated content manager
 * @param enableUntruncatedContentStorage Whether to store untruncated content (feature flag)
 * @param maxLinesTerminalProcessOutput Maximum number of lines to show from the end (0 = use default truncation)
 * @returns The truncated content with metadata and the metadata object
 */
export async function truncateWithMetadata(
  content: string,
  options: TruncateOptions,
  contentManager: UntruncatedContentManager,
  enableUntruncatedContentStorage: boolean,
  maxLinesTerminalProcessOutput: number,
): Promise<TruncateResult> {
  const lines = content.split("\n");
  const totalLines = lines.length;
  const totalSize = content.length;

  // Handle maxLinesTerminalProcessOutput truncation (if enabled)
  if (
    maxLinesTerminalProcessOutput > 0 &&
    totalLines > maxLinesTerminalProcessOutput
  ) {
    const lastNLines = lines.slice(-maxLinesTerminalProcessOutput);
    const truncatedContent = lastNLines.join("\n");

    const shownRange: [number, number] = [
      totalLines - maxLinesTerminalProcessOutput + 1,
      totalLines,
    ];

    const metadata = await contentManager.storeUntruncatedContent(
      content,
      options.contentType,
      shownRange,
      options.toolUseId,
      options.requestId,
      options.conversationId,
    );

    // Add standardized metadata footer
    const metadataFooter = createTruncationFooter(metadata);
    const contentWithFooter = `${truncatedContent}\n\n${metadataFooter}`;

    return {
      truncatedContent: contentWithFooter,
      metadata,
    };
  }

  if (totalSize <= options.maxBytes) {
    return {
      truncatedContent: content,
    };
  }

  const { truncatedText, shownRangeWhenTruncated } = truncateMiddleHelper(
    content,
    options.maxBytes,
  );

  // Return truncated content without metadata when storage is disabled
  if (!enableUntruncatedContentStorage) {
    return {
      truncatedContent: truncatedText,
    };
  }

  // Store the full content based on feature flag
  const metadata = await contentManager.storeUntruncatedContent(
    content,
    options.contentType,
    shownRangeWhenTruncated || [1, totalLines],
    options.toolUseId,
    options.requestId,
    options.conversationId,
  );

  // Add standardized metadata footer
  const metadataFooter = createTruncationFooter(metadata);
  const truncatedContent = `${truncatedText}\n\n${metadataFooter}`;

  return {
    truncatedContent,
    metadata,
  };
}

/**
 * Creates a standardized footer for truncated content
 *
 * @param metadata The truncation metadata
 * @returns A formatted footer string
 */
export function createTruncationFooter(
  metadata: TruncatedContentMetadata,
): string {
  const { shownRange, totalLines, referenceId } = metadata;

  // Check if we have a complex range (showing both beginning and end)
  let rangeText: string;
  if (shownRange.length === 4) {
    // Format as "lines 1-50, 150-200 of 200 lines"
    rangeText = `lines ${shownRange[0]}-${shownRange[1]}, ${shownRange[2]}-${shownRange[3]} of ${totalLines} lines`;
  } else {
    // Format as "lines 1-50 of 200 lines"
    rangeText = `lines ${shownRange[0]}-${shownRange[1]} of ${totalLines} lines`;
  }

  return (
    `[This result was truncated. Showing ${rangeText}. ` +
    `Use view-range-untruncated or search-untruncated tools to access the full content. ` +
    `Reference ID: ${referenceId}]`
  );
}
