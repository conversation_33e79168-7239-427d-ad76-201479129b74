from services.inference_host.client.inference_host_client import (
    InferenceClientProtocol,
    InfererClient,
)
from base.caching.lru_cache import LRUCache
from dataclasses import dataclass
from collections.abc import Iterable
from services.lib.request_context.request_context import RequestContext
from services.next_edit_host.server.next_edit_utils import SamplingParams
from pympler import asizeof


@dataclass(frozen=True)
class InferenceCallInput:
    prompt: tuple[int, ...]
    max_output_tokens: int


class LRUInferenceCache(
    LRUCache[InferenceCallInput, InferenceClientProtocol.Reply, ...]
):
    def __init__(
        self,
        edit_gen_client: InfererClient,
    ):
        # assuming an average prompt length of 10K tokens, we should not need more than
        # ~40 MB of memory
        super().__init__(
            get_missing_fn=self._get_missing,
            max_size=40 * 1024 * 1024,  # 40MB
            cache_missing_keys=False,
            name="LRUInferenceCache",
            size_fn=self._size_fn,
        )
        self.edit_gen_client = edit_gen_client

    def _get_missing(
        self,
        keys: Iterable[InferenceCallInput],
        request_context: RequestContext,
        end_token_ids: tuple[int, ...],
        cur_sampling_params: SamplingParams,
        random_seed: int,
        sequence_id: int,
        priority: int,
        max_output_length: int,
    ) -> Iterable[InferenceClientProtocol.Reply | None]:
        for key in keys:
            reply = self.edit_gen_client.infer(
                input_tokens=key.prompt,
                max_output_length=max_output_length,
                end_token_ids=end_token_ids,
                top_k=cur_sampling_params.top_k,
                top_p=cur_sampling_params.top_p,
                temperature=cur_sampling_params.temperature,
                random_seed=random_seed,
                request_context=request_context,
                timeout_s=cur_sampling_params.inference_timeout_s,
                sequence_id=sequence_id,
                priority=priority,
            )
            yield reply

    def _size_fn(
        self, key: InferenceCallInput, value: InferenceClientProtocol.Reply | None
    ) -> int:
        key_bytes = asizeof.asizeof(key.prompt)
        value_bytes = asizeof.asizeof(value.output_tokens) if value is not None else 0
        return key_bytes + value_bytes
