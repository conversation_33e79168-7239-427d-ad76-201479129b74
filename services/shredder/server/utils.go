package main

import pb "github.com/augmentcode/augment/services/shredder/proto"

// Misc. utils that don't belong anywhere in particular.

func deletionRequestType(deletion *pb.DeletionInfo) string {
	ref := deletion.Request.ProtoReflect()
	return string(ref.WhichOneof(ref.Descriptor().Oneofs().ByName("request")).Name())
}

func taskType(task *pb.TaskInfo) string {
	ref := task.GetDetails().ProtoReflect()
	return string(ref.WhichOneof(ref.Descriptor().Oneofs().ByName("task")).Name())
}
