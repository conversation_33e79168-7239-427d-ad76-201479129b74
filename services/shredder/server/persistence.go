package main

import (
	"context"
	"fmt"
	"time"

	"cloud.google.com/go/spanner"
	pb "github.com/augmentcode/augment/services/shredder/proto"
	"github.com/rs/zerolog/log"
	"google.golang.org/api/iterator"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type SpannerConfig struct {
	// Full name of the database, in the form
	// "projects/<project>/instances/<instance>/databases/<database>".
	DatabaseFullName string
}

type ShredderPersistence struct {
	spannerClient *spanner.Client
}

// Represents a row in the Deletion table.
type deletionRow struct {
	DeletionID   string
	Status       string
	RequestType  string
	UserID       string
	UserEmail    string
	Reason       string
	CreatedAt    time.Time
	UpdatedAt    spanner.NullTime
	CompletedAt  spanner.NullTime
	RequestProto []byte
}

// Represents a row in the Task table.
type taskRow struct {
	DeletionID   string
	TaskID       string
	SequenceID   int64
	Status       string
	StartedAt    spanner.NullTime
	UpdatedAt    spanner.NullTime
	CompletedAt  spanner.NullTime
	TaskType     string
	DetailsProto []byte
}

func NewShredderPersistence(
	ctx context.Context, config *SpannerConfig,
) (*ShredderPersistence, error) {
	spannerClient, err := spanner.NewClient(ctx, config.DatabaseFullName)
	if err != nil {
		return nil, fmt.Errorf("failed to create spanner client: %w", err)
	}

	return &ShredderPersistence{
		spannerClient: spannerClient,
	}, nil
}

func (s *ShredderPersistence) Close() {
	s.spannerClient.Close()
}

// Adds the provided deletion and tasks to the database. Returns the ID of the appended deletion.
// This may not match the ID of the provided deletion if a pending deletion of the same type already
// existed for this user. Returns an error if a different type of pending deletion already exists
// for this user.
func (s *ShredderPersistence) CreateDeletion(
	ctx context.Context, deletion *pb.DeletionInfo, tasks []*pb.TaskInfo,
) (string, error) {
	var deletionID string
	_, err := s.spannerClient.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spanner.ReadWriteTransaction) error {
		// Check for pending deletions for this user.
		pendingDeletionsForUserIter := txn.Query(ctx, spanner.Statement{
			SQL: `
		    SELECT DeletionID, RequestType
		    FROM Deletion@{FORCE_INDEX=Deletion_UserID_Status}
		    WHERE UserID = @userID AND Status = @status
		  `,
			Params: map[string]interface{}{
				"userID": deletion.GetRequest().GetUserId(),
				"status": pb.DeletionInfo_PENDING.String(),
			},
		})
		defer pendingDeletionsForUserIter.Stop()
		pendingDeletionForUser, err := pendingDeletionsForUserIter.Next()
		if err != nil && err != iterator.Done {
			return fmt.Errorf("failed to check for pending deletions: %w", err)
		}
		if pendingDeletionForUser != nil {
			var existingDeletionID string
			var existingRequestType string
			if err := pendingDeletionForUser.Columns(&existingDeletionID, &existingRequestType); err != nil {
				return fmt.Errorf("failed to get pending deletion info: %w", err)
			}

			if existingRequestType != deletionRequestType(deletion) {
				// TODO(jacqueline): This should probably be a special error type so that we can show
				// something nice to the user.
				return fmt.Errorf("user already has a pending deletion of a different type")
			}

			log.Ctx(ctx).Info().Msgf(
				"User %s already has a pending %s deletion, reusing it: %s",
				deletion.GetRequest().GetUserId(), existingRequestType, existingDeletionID)
			deletionID = existingDeletionID
			return nil
		}

		// Collect mutations to be executed at the end of the transaction.
		mutations := []*spanner.Mutation{}

		// Create the deletion row.
		deletionRow, err := deletionProtoToSpannerRow(deletion)
		if err != nil {
			return fmt.Errorf("failed to convert deletion to spanner row: %w", err)
		}
		deletionInsertion, err := spanner.InsertStruct("Deletion", deletionRow)
		if err != nil {
			return fmt.Errorf("failed to insert deletion row: %w", err)
		}
		mutations = append(mutations, deletionInsertion)

		// Create the task rows.
		for i, task := range tasks {
			taskRow, err := taskProtoToSpannerRow(deletion.GetDeletionId(), i, task)
			if err != nil {
				return fmt.Errorf("failed to convert task to spanner row: %w", err)
			}
			taskInsertion, err := spanner.InsertStruct("Task", taskRow)
			if err != nil {
				return fmt.Errorf("failed to insert task row: %w", err)
			}
			mutations = append(mutations, taskInsertion)
		}

		deletionID = deletion.GetDeletionId()
		return txn.BufferWrite(mutations)
	})

	if err != nil {
		return "", fmt.Errorf("failed to create deletion: %w", err)
	} else if deletionID == "" {
		// This is a bug.
		log.Ctx(ctx).Error().Msgf("No deletion set by CreateDeletion transaction")
		return "", fmt.Errorf("failed to create deletion: no deletion ID returned")
	}
	return deletionID, nil
}

func (s *ShredderPersistence) GetDeletionAndTasks(
	ctx context.Context, deletionID string,
) (*pb.DeletionInfo, []*pb.TaskInfo, error) {
	// This transaction is done in two queries instead of one (with a join) because Jacqueline
	// couldn't figure out how to get the client library's struct parsing to work with a join, and
	// we're not particularly concerned about latency for this service.
	txn := s.spannerClient.ReadOnlyTransaction()
	defer txn.Close()

	// Read the deletion row.
	deletionStmt := spanner.Statement{
		SQL: `
			SELECT *
			FROM Deletion
			WHERE DeletionID = @deletionID
		`,
		Params: map[string]interface{}{
			"deletionID": deletionID,
		},
	}
	deletionIter := txn.Query(ctx, deletionStmt)
	defer deletionIter.Stop()
	delRow, err := deletionIter.Next()
	if err == iterator.Done {
		return nil, nil, nil
	} else if err != nil {
		return nil, nil, fmt.Errorf("failed to read deletion row: %w", err)
	}

	var delRowStruct deletionRow
	if err := delRow.ToStruct(&delRowStruct); err != nil {
		return nil, nil, fmt.Errorf("failed to convert deletion row to struct: %w", err)
	}
	deletion, err := delRowStruct.ToProto()
	if err != nil {
		return nil, nil, fmt.Errorf("failed to convert deletion row to proto: %w", err)
	}

	// Read the task rows.
	taskStmt := spanner.Statement{
		SQL: `
			SELECT *
			FROM Task
			WHERE DeletionID = @deletionID
			ORDER BY SequenceID
		`,
		Params: map[string]interface{}{
			"deletionID": deletionID,
		},
	}
	taskIter := txn.Query(ctx, taskStmt)
	defer taskIter.Stop()

	var tasks []*pb.TaskInfo
	for {
		row, err := taskIter.Next()
		if err == iterator.Done {
			break
		} else if err != nil {
			return nil, nil, fmt.Errorf("failed to read task row: %w", err)
		}

		var taskRow taskRow
		if err := row.ToStruct(&taskRow); err != nil {
			return nil, nil, fmt.Errorf("failed to convert task row to struct: %w", err)
		}
		task, err := taskRow.ToProto()
		if err != nil {
			return nil, nil, fmt.Errorf("failed to convert task row to proto: %w", err)
		}
		tasks = append(tasks, task)
	}

	return deletion, tasks, nil
}

func (s *ShredderPersistence) ListDeletions(
	ctx context.Context, pageSize uint32,
) ([]*pb.DeletionInfo, error) {
	stmt := spanner.Statement{
		SQL: `
			SELECT *
			FROM Deletion@{FORCE_INDEX=Deletion_CreatedAt}
			ORDER BY CreatedAt DESC, DeletionID
			LIMIT @limit
		`,
		Params: map[string]interface{}{
			"limit": int64(pageSize),
		},
	}
	iter := s.spannerClient.Single().Query(ctx, stmt)
	defer iter.Stop()

	var deletions []*pb.DeletionInfo
	for {
		row, err := iter.Next()
		if err == iterator.Done {
			break
		} else if err != nil {
			return nil, fmt.Errorf("failed to read deletion row: %w", err)
		}

		var delRow deletionRow
		if err := row.ToStruct(&delRow); err != nil {
			return nil, fmt.Errorf("failed to convert deletion row to struct: %w", err)
		}
		deletion, err := delRow.ToProto()
		if err != nil {
			return nil, fmt.Errorf("failed to convert deletion row to proto: %w", err)
		}
		deletions = append(deletions, deletion)
	}

	return deletions, nil
}

func deletionProtoToSpannerRow(deletion *pb.DeletionInfo) (*deletionRow, error) {
	requestBytes, err := proto.Marshal(deletion.GetRequest())
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	row := &deletionRow{
		DeletionID:   deletion.GetDeletionId(),
		Status:       deletion.GetStatus().String(),
		CreatedAt:    deletion.GetCreatedAt().AsTime(),
		RequestType:  deletionRequestType(deletion),
		UserID:       deletion.GetRequest().GetUserId(),
		UserEmail:    deletion.GetUserEmail(),
		Reason:       deletion.GetRequest().GetReason(),
		RequestProto: requestBytes,
	}
	if deletion.GetUpdatedAt() != nil {
		row.UpdatedAt = spanner.NullTime{Valid: true, Time: deletion.GetUpdatedAt().AsTime()}
	}
	if deletion.GetCompletedAt() != nil {
		row.CompletedAt = spanner.NullTime{Valid: true, Time: deletion.GetCompletedAt().AsTime()}
	}

	return row, nil
}

func taskProtoToSpannerRow(deletionID string, sequenceID int, task *pb.TaskInfo) (*taskRow, error) {
	taskDetailsBytes, err := proto.Marshal(task.GetDetails())
	if err != nil {
		return nil, fmt.Errorf("failed to marshal task details: %w", err)
	}

	row := &taskRow{
		DeletionID:   deletionID,
		TaskID:       task.GetTaskId(),
		SequenceID:   int64(sequenceID),
		Status:       task.GetStatus().String(),
		TaskType:     taskType(task),
		DetailsProto: taskDetailsBytes,
	}
	if task.GetStartedAt() != nil {
		row.StartedAt = spanner.NullTime{Valid: true, Time: task.GetStartedAt().AsTime()}
	}
	if task.GetUpdatedAt() != nil {
		row.UpdatedAt = spanner.NullTime{Valid: true, Time: task.GetUpdatedAt().AsTime()}
	}
	if task.GetCompletedAt() != nil {
		row.CompletedAt = spanner.NullTime{Valid: true, Time: task.GetCompletedAt().AsTime()}
	}

	return row, nil
}

func (r *deletionRow) ToProto() (*pb.DeletionInfo, error) {
	req := &pb.EnqueueUserDataDeletionRequest{}
	if err := proto.Unmarshal(r.RequestProto, req); err != nil {
		return nil, fmt.Errorf("failed to unmarshal deletion request proto: %w", err)
	}

	deletion := &pb.DeletionInfo{
		DeletionId: r.DeletionID,
		Status:     pb.DeletionInfo_Status(pb.DeletionInfo_Status_value[r.Status]),
		CreatedAt:  timestamppb.New(r.CreatedAt),
		UserEmail:  r.UserEmail,
		Request:    req,
	}
	if r.UpdatedAt.Valid {
		deletion.UpdatedAt = timestamppb.New(r.UpdatedAt.Time)
	}
	if r.CompletedAt.Valid {
		deletion.CompletedAt = timestamppb.New(r.CompletedAt.Time)
	}

	return deletion, nil
}

func (r *taskRow) ToProto() (*pb.TaskInfo, error) {
	taskDetails := &pb.TaskDetails{}
	if err := proto.Unmarshal(r.DetailsProto, taskDetails); err != nil {
		return nil, fmt.Errorf("failed to unmarshal task details proto: %w", err)
	}

	task := &pb.TaskInfo{
		TaskId:  r.TaskID,
		Status:  pb.TaskInfo_Status(pb.TaskInfo_Status_value[r.Status]),
		Details: taskDetails,
	}
	if r.StartedAt.Valid {
		task.StartedAt = timestamppb.New(r.StartedAt.Time)
	}
	if r.UpdatedAt.Valid {
		task.UpdatedAt = timestamppb.New(r.UpdatedAt.Time)
	}
	if r.CompletedAt.Valid {
		task.CompletedAt = timestamppb.New(r.CompletedAt.Time)
	}

	return task, nil
}
