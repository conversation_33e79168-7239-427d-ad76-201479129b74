local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpointsLib = import 'services/deploy/endpoints.jsonnet';

function(env, namespace, cloud, namespace_config)
  local appName = 'shredder';
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local dynamicFeatureFlags = dynamicFeatureFlagsLib.createLaunchDarklySecret(env=env, namespace=namespace, cloud=cloud, appName=appName);

  local clientCert = certLib.createCentralClientCert(
    name='%s-client-cert' % appName,
    namespace=namespace,
    env=env,
    appName=appName,
    volumeName='client-certs',
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
  );
  local serverCert = certLib.createCentralServerCert(
    name='%s-server-certificate' % appName,
    namespace=namespace,
    env=env,
    appName=appName,
    dnsNames=grpcLib.grpcServiceNames(appName),
    volumeName='certs',
  );

  local serviceAccount = gcpLib.createServiceAccount(
    appName, env, cloud, namespace, iam=true,
  );

  // Give access to the database.
  local spannerInfo = gcpLib.getSpannerDatabaseInfo(cloud, env, namespace, 'shredder');
  local spannerAccess = gcpLib.grantAccess(
    name='%s-database-grant' % appName,
    env=env,
    namespace=namespace,
    appName=appName,
    resourceRef={
      kind: 'SpannerDatabase',
      external: spannerInfo.databaseFullName,
    },
    bindings=[
      {
        role: 'roles/spanner.databaseUser',
        members: [
          {
            memberFrom: {
              serviceAccountRef: {
                name: serviceAccount.iamServiceAccountName,
              },
            },
          },
        ],
      },
    ],
  );

  // Give monitoring.metricWriter so that the spanner client can record metrics. You're supposed to
  // get this from spanner.databaseUser but it seems like you need this at a project level rather
  // than a database level.
  local monitoringAccess = gcpLib.grantAccess(
    name='%s-monitoring-grant' % appName,
    env=env,
    namespace=namespace,
    appName=appName,
    resourceRef={
      kind: 'Project',
      external: cloudInfo[cloud].projectId,
    },
    bindings=[
      {
        role: 'roles/monitoring.metricWriter',
        members: [
          {
            memberFrom: {
              serviceAccountRef: {
                name: serviceAccount.iamServiceAccountName,
              },
            },
          },
        ],
      },
    ],
  );

  local config = {
    port: 50051,
    serverMtls: if mtls then serverCert.config else null,
    clientMtls: if mtls then clientCert.config else null,
    promPort: 9090,
    tokenExchangeEndpoint: endpointsLib.getTokenExchangeGrpcUrl(env=env, namespace=namespace, cloud=cloud),
    authCentralEndpoint: '%s:50051' % endpointsLib.getAuthCentralGrpcUrl(env=env, cloud=cloud, namespace=namespace),
    featureFlagsSdkKeyPath: dynamicFeatureFlags.secretsFilePath,
    dynamicFeatureFlagsEndpoint: if namespace_config.flags.useFakeFeatureFlags then 'http://fake-feature-flags-svc' else null,
    spannerConfig: {
      databaseFullName: spannerInfo.databaseFullName,
    },
  };
  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);

  local services = grpcLib.grpcService(appName=appName, namespace=namespace);
  local container = {
    name: appName,
    target: {
      name: '//services/shredder/server:image',
      dst: 'shredder',
    },
    args: [
      '--config',
      configMap.filename,
    ],
    ports: [
      {
        containerPort: 50051,
        name: 'grpc-svc',
      },
    ],
    env: telemetryLib.telemetryEnv(appName, telemetryLib.collectorUri(env, namespace, cloud)) + dynamicFeatureFlags.env,
    volumeMounts: [
      configMap.volumeMountDef,
      serverCert.volumeMountDef,
      clientCert.volumeMountDef,
      dynamicFeatureFlags.volumeMountDef,
    ],
    readinessProbe: grpcLib.grpcHealthCheck(appName + '-svc', tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
      periodSeconds: 30,
    },
    livenessProbe: grpcLib.grpcHealthCheck(appName + '-svc', tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
      periodSeconds: 30,
    },
    resources: {
      limits: {
        cpu: 1,
        memory: '2Gi',
      },
    },
  };
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local pod = {
    serviceAccountName: serviceAccount.name,
    containers: [
      container,
    ],
    priorityClassName: cloudInfo.envToPriorityClass(env),
    affinity: affinity,
    tolerations: tolerations,
    volumes: [
      configMap.podVolumeDef,
      serverCert.podVolumeDef,
      clientCert.podVolumeDef,
      dynamicFeatureFlags.podVolumeDef,
    ],
  };

  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      minReadySeconds: if env == 'DEV' then 0 else 60,
      replicas: if env == 'DEV' then 1 else 2,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod + {
          tolerations: tolerations,
          affinity: affinity,
        },
      },
    },
  };
  lib.flatten([
    configMap.objects,
    serviceAccount.objects,
    spannerAccess,
    monitoringAccess,
    serverCert.objects,
    clientCert.objects,
    dynamicFeatureFlags.k8s_objects,
    deployment,
    services,
  ])
