package main

import (
	"context"
	"os"
	"testing"

	"cloud.google.com/go/spanner"
	spanneremulator "github.com/augmentcode/augment/base/test_utils/spanner/emulator"
)

// This file sets up shared test fixtures.

const (
	projectID = "test-project"
)

// Shared test fixtures. These are initialized in TestMain.
var (
	spannerEmulator *spanneremulator.SpannerEmulator = nil
)

func TestMain(m *testing.M) {
	emulator, err := spanneremulator.New(context.Background())
	if err != nil {
		panic(err)
	}
	defer emulator.Close()
	spannerEmulator = emulator

	// Run the tests.
	os.Exit(m.Run())
}

// Recommended use of the spanner emulator is to initialize a new database for each test.
func NewShredderDatabase(ctx context.Context) (*spanner.Client, func()) {
	client, cleanup, err := spannerEmulator.NewDatabaseFromJson(ctx, "spanner_ddl.json")
	if err != nil {
		panic(err)
	}
	return client, cleanup
}
