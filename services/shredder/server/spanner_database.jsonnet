local lib = import 'deploy/common/lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local ddl = import 'services/shredder/server/spanner_ddl.jsonnet';

// Spanner database creation is pulled out into its own kubecfg target so that we can make sure we
// deploy the database before code that relies on it.
function(cloud, env, namespace, namespace_config)
  local appName = 'shredder-spanner-database';
  local database = gcpLib.createSpannerDatabase(
    cloud,
    env,
    namespace,
    appName,
    'shredder',
    ddl,
  );

  lib.flatten([
    database.objects,
  ])
