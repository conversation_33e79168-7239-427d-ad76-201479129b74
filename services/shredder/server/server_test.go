package main

import (
	"context"
	"fmt"
	"testing"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging/audit"
	authclient "github.com/augmentcode/augment/services/auth/central/auth_client"
	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	pb "github.com/augmentcode/augment/services/shredder/proto"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type MockAuthClient struct {
	authclient.AuthClient
	mock.Mock
}

func (m *MockAuthClient) GetUser(
	ctx context.Context, requestContext *requestcontext.RequestContext, userId string, tenantID *string,
) (*auth_entities.User, error) {
	args := m.Called(ctx, requestContext, userId, tenantID)
	user := &auth_entities.User{
		Id:    userId,
		Email: fmt.Sprintf("%<EMAIL>", userId),
	}
	return user, args.Error(1)
}

func testServer(t *testing.T) (*shredderServer, func()) {
	database, cleanup := NewShredderDatabase(context.Background())
	persistence := &ShredderPersistence{
		spannerClient: database,
	}

	// Mock out the auth client.
	authClient := new(MockAuthClient)
	authClient.On(
		"GetUser", mock.Anything, mock.Anything, mock.AnythingOfType("string"), mock.Anything,
	).Return(nil, nil) // The actual user will be created in the mock's GetUser method

	// Create a test server
	return &shredderServer{
		featureFlagHandle: featureflags.NewLocalFeatureFlagHandler(),
		auditLogger:       audit.NewDefaultAuditLogger(),
		persistence:       persistence,
		authClient:        authClient,
	}, cleanup
}

func iapContext(scope string) context.Context {
	claims := &auth.AugmentClaims{
		TenantID:         "",
		TenantName:       "",
		UserID:           "<EMAIL>",
		OpaqueUserID:     "iap:<EMAIL>",
		OpaqueUserIDType: "INTERNAL_IAP",
		Scope:            []string{scope},
	}
	ctx := claims.NewContext(context.Background())
	ctx = requestcontext.NewIncomingContext(ctx, &requestcontext.RequestContext{
		RequestId:        requestcontext.NewRandomRequestId(),
		RequestSessionId: requestcontext.NewRandomRequestSessionId(),
		RequestSource:    "test-source",
	})
	return requestcontext.NewOutgoingContext(ctx, &requestcontext.RequestContext{})
}

func userContext(userID, scope string) context.Context {
	claims := &auth.AugmentClaims{
		TenantID:         "test-tenant",
		TenantName:       "test-tenant",
		UserID:           userID,
		OpaqueUserID:     userID,
		OpaqueUserIDType: "AUGMENT",
		Scope:            []string{scope},
	}
	ctx := claims.NewContext(context.Background())
	ctx = requestcontext.NewIncomingContext(ctx, &requestcontext.RequestContext{
		RequestId:        requestcontext.NewRandomRequestId(),
		RequestSessionId: requestcontext.NewRandomRequestSessionId(),
		RequestSource:    "test-source",
	})
	return requestcontext.NewOutgoingContext(ctx, &requestcontext.RequestContext{})
}

func checkDeletion(t *testing.T, expected *pb.DeletionInfo, actual *pb.DeletionInfo) {
	// Check that timestamps are nil or not appropriately.
	require.Equal(t, expected.CreatedAt == nil, actual.CreatedAt == nil)
	require.Equal(t, expected.UpdatedAt == nil, actual.UpdatedAt == nil)
	require.Equal(t, expected.CompletedAt == nil, actual.CompletedAt == nil)

	// Clear out timestamps for proto comparison
	expected.CreatedAt = nil
	expected.UpdatedAt = nil
	expected.CompletedAt = nil
	actual.CreatedAt = nil
	actual.UpdatedAt = nil
	actual.CompletedAt = nil

	require.True(t, proto.Equal(expected, actual), "Expected: %v, got: %v", expected, actual)
}

func checkTask(t *testing.T, expected *pb.TaskInfo, actual *pb.TaskInfo) {
	// Check that id is non-empty.
	require.NotEmpty(t, actual.TaskId)

	// Check that timestamps are nil or not appropriately.
	require.Equal(t, expected.StartedAt == nil, actual.StartedAt == nil)
	require.Equal(t, expected.UpdatedAt == nil, actual.UpdatedAt == nil)
	require.Equal(t, expected.CompletedAt == nil, actual.CompletedAt == nil)

	// Clear out timestamps for proto comparison
	expected.StartedAt = nil
	expected.UpdatedAt = nil
	expected.CompletedAt = nil
	actual.StartedAt = nil
	actual.UpdatedAt = nil
	actual.CompletedAt = nil
	actual.TaskId = ""

	require.True(t, proto.Equal(expected, actual), "Expected: %v, got: %v", expected, actual)
}

func checkTasks(t *testing.T, expected []*pb.TaskInfo, actual []*pb.TaskInfo) {
	require.Len(t, actual, len(expected))
	for i, task := range actual {
		checkTask(t, expected[i], task)
	}
}

func TestEnqueueUserDataDeletion(t *testing.T) {
	t.Run("auth", func(t *testing.T) {
		server, cleanup := testServer(t)
		defer cleanup()

		req := &pb.EnqueueUserDataDeletionRequest{
			UserId: "test-user",
			Reason: "test-reason",
			Request: &pb.EnqueueUserDataDeletionRequest_ContentDeletion{
				ContentDeletion: &pb.ContentDeletionRequest{},
			},
		}

		// Missing auth claims.
		_, err := server.EnqueueUserDataDeletion(context.Background(), req)
		require.Error(t, err)
		require.Equal(t, codes.PermissionDenied, status.Code(err))

		// Incorrect scope.
		_, err = server.EnqueueUserDataDeletion(iapContext("CONTENT_R"), req)
		require.Error(t, err)
		require.Equal(t, codes.PermissionDenied, status.Code(err))

		// Incorrect user id.
		_, err = server.EnqueueUserDataDeletion(
			userContext("wrong-user", "CONTENT_ADMIN"), req)
		require.Error(t, err)
		require.Equal(t, codes.PermissionDenied, status.Code(err))
	})

	t.Run("arg validation", func(t *testing.T) {
		server, cleanup := testServer(t)
		defer cleanup()

		badReqs := []*pb.EnqueueUserDataDeletionRequest{
			// Missing user id.
			{
				Reason: "test-reason",
				Request: &pb.EnqueueUserDataDeletionRequest_ContentDeletion{
					ContentDeletion: &pb.ContentDeletionRequest{},
				},
			},
			// Missing reason.
			{
				UserId: "test-user",
				Request: &pb.EnqueueUserDataDeletionRequest_ContentDeletion{
					ContentDeletion: &pb.ContentDeletionRequest{},
				},
			},
			// Missing request.
			{
				UserId: "test-user",
				Reason: "test-reason",
			},
		}

		for _, req := range badReqs {
			_, err := server.EnqueueUserDataDeletion(context.Background(), req)
			require.Error(t, err)
			require.Equal(t, codes.InvalidArgument, status.Code(err))
		}
	})

	t.Run("content deletion", func(t *testing.T) {
		server, cleanup := testServer(t)
		defer cleanup()

		// Enqueue a request.
		req := &pb.EnqueueUserDataDeletionRequest{
			UserId: "test-user",
			Reason: "test-reason",
			Request: &pb.EnqueueUserDataDeletionRequest_ContentDeletion{
				ContentDeletion: &pb.ContentDeletionRequest{},
			},
		}
		resp, err := server.EnqueueUserDataDeletion(iapContext("CONTENT_ADMIN"), req)
		require.NoError(t, err)
		require.NotEmpty(t, resp.DeletionId)

		dbDeletion, dbTasks, err := server.persistence.GetDeletionAndTasks(context.Background(), resp.DeletionId)
		require.NoError(t, err)
		checkDeletion(t, &pb.DeletionInfo{
			DeletionId: resp.DeletionId,
			Status:     pb.DeletionInfo_PENDING,
			Request:    req,
			UserEmail:  "<EMAIL>",
			CreatedAt:  timestamppb.Now(),
		}, dbDeletion)

		expectedTasks := []*pb.TaskInfo{
			{
				Status:  pb.TaskInfo_BLOCKED,
				Details: &pb.TaskDetails{Task: &pb.TaskDetails_VanguardExportDeleteRequestData{}},
			},
			{
				Status:  pb.TaskInfo_BLOCKED,
				Details: &pb.TaskDetails{Task: &pb.TaskDetails_VanguardExportDeleteBlobs{}},
			},
			{
				Status:  pb.TaskInfo_BLOCKED,
				Details: &pb.TaskDetails{Task: &pb.TaskDetails_ContentManagerDeleteBlobs{}},
			},
			{
				Status:  pb.TaskInfo_BLOCKED,
				Details: &pb.TaskDetails{Task: &pb.TaskDetails_RemoteAgentsDeleteData{}},
			},
		}
		checkTasks(t, expectedTasks, dbTasks)
	})

	t.Run("account deletion", func(t *testing.T) {
		server, cleanup := testServer(t)
		defer cleanup()

		// Enqueue a request.
		resp, err := server.EnqueueUserDataDeletion(
			iapContext("AUTH_RW"),
			&pb.EnqueueUserDataDeletionRequest{
				UserId: "test-user",
				Reason: "test-reason",
				Request: &pb.EnqueueUserDataDeletionRequest_AccountDeletion{
					AccountDeletion: &pb.AccountDeletionRequest{},
				},
			},
		)
		require.NoError(t, err)
		require.NotEmpty(t, resp.DeletionId)

		dbDeletion, dbTasks, err := server.persistence.GetDeletionAndTasks(context.Background(), resp.DeletionId)
		require.NoError(t, err)
		checkDeletion(t, &pb.DeletionInfo{
			DeletionId: resp.DeletionId,
			Status:     pb.DeletionInfo_PENDING,
			Request: &pb.EnqueueUserDataDeletionRequest{
				UserId: "test-user",
				Reason: "test-reason",
				Request: &pb.EnqueueUserDataDeletionRequest_AccountDeletion{
					AccountDeletion: &pb.AccountDeletionRequest{},
				},
			},
			UserEmail: "<EMAIL>",
			CreatedAt: timestamppb.Now(),
		}, dbDeletion)

		expectedTasks := []*pb.TaskInfo{
			{
				Status:  pb.TaskInfo_BLOCKED,
				Details: &pb.TaskDetails{Task: &pb.TaskDetails_ContentManagerDeleteBlobs{}},
			},
			{
				Status:  pb.TaskInfo_BLOCKED,
				Details: &pb.TaskDetails{Task: &pb.TaskDetails_RemoteAgentsDeleteData{}},
			},
			{
				Status:  pb.TaskInfo_BLOCKED,
				Details: &pb.TaskDetails{Task: &pb.TaskDetails_SettingsDeleteUserSettings{}},
			},
			{
				Status:  pb.TaskInfo_BLOCKED,
				Details: &pb.TaskDetails{Task: &pb.TaskDetails_AuthCentralDeleteAccount{}},
			},
		}
		checkTasks(t, expectedTasks, dbTasks)
	})

	t.Run("gdpr/ccpa deletion", func(t *testing.T) {
		server, cleanup := testServer(t)
		defer cleanup()

		// Enqueue a request.
		resp, err := server.EnqueueUserDataDeletion(
			iapContext("PII_ADMIN"),
			&pb.EnqueueUserDataDeletionRequest{
				UserId: "test-user",
				Reason: "test-reason",
				Request: &pb.EnqueueUserDataDeletionRequest_GdprCcpaDeletion{
					GdprCcpaDeletion: &pb.GdprCcpaDeletionRequest{},
				},
			},
		)
		require.NoError(t, err)
		require.NotEmpty(t, resp.DeletionId)

		dbDeletion, dbTasks, err := server.persistence.GetDeletionAndTasks(context.Background(), resp.DeletionId)
		require.NoError(t, err)
		checkDeletion(t, &pb.DeletionInfo{
			DeletionId: resp.DeletionId,
			Status:     pb.DeletionInfo_PENDING,
			Request: &pb.EnqueueUserDataDeletionRequest{
				UserId: "test-user",
				Reason: "test-reason",
				Request: &pb.EnqueueUserDataDeletionRequest_GdprCcpaDeletion{
					GdprCcpaDeletion: &pb.GdprCcpaDeletionRequest{},
				},
			},
			UserEmail: "<EMAIL>",
			CreatedAt: timestamppb.Now(),
		}, dbDeletion)

		expectedTasks := []*pb.TaskInfo{
			{
				Status:  pb.TaskInfo_BLOCKED,
				Details: &pb.TaskDetails{Task: &pb.TaskDetails_VanguardExportDeleteRequestData{}},
			},
			{
				Status:  pb.TaskInfo_BLOCKED,
				Details: &pb.TaskDetails{Task: &pb.TaskDetails_VanguardExportDeleteBlobs{}},
			},
			{
				Status:  pb.TaskInfo_BLOCKED,
				Details: &pb.TaskDetails{Task: &pb.TaskDetails_ContentManagerDeleteBlobs{}},
			},
			{
				Status:  pb.TaskInfo_BLOCKED,
				Details: &pb.TaskDetails{Task: &pb.TaskDetails_RemoteAgentsDeleteData{}},
			},
			{
				Status:  pb.TaskInfo_BLOCKED,
				Details: &pb.TaskDetails{Task: &pb.TaskDetails_SettingsDeleteUserSettings{}},
			},
			{
				Status:  pb.TaskInfo_BLOCKED,
				Details: &pb.TaskDetails{Task: &pb.TaskDetails_AuthCentralDeleteAccount{}},
			},
			{
				Status:  pb.TaskInfo_BLOCKED,
				Details: &pb.TaskDetails{Task: &pb.TaskDetails_AnalyticsForgetUser{}},
			},
			{
				Status:  pb.TaskInfo_BLOCKED,
				Details: &pb.TaskDetails{Task: &pb.TaskDetails_AuthCentralForgetUser{}},
			},
		}
		checkTasks(t, expectedTasks, dbTasks)
	})

	// Test that enqueuing the same request twice returns the same deletion id.
	t.Run("idempotence", func(t *testing.T) {
		server, cleanup := testServer(t)
		defer cleanup()

		req := &pb.EnqueueUserDataDeletionRequest{
			UserId: "test-user",
			Reason: "test-reason",
			Request: &pb.EnqueueUserDataDeletionRequest_ContentDeletion{
				ContentDeletion: &pb.ContentDeletionRequest{},
			},
		}

		// Enqueue a request.
		resp1, err := server.EnqueueUserDataDeletion(iapContext("CONTENT_ADMIN"), req)
		require.NoError(t, err)
		require.NotEmpty(t, resp1.DeletionId)

		// Enqueue the same request again.
		resp2, err := server.EnqueueUserDataDeletion(iapContext("CONTENT_ADMIN"), req)
		require.NoError(t, err)
		require.Equal(t, resp1.DeletionId, resp2.DeletionId)
	})

	t.Run("one pending deletion per user", func(t *testing.T) {
		server, cleanup := testServer(t)
		defer cleanup()

		// Enqueue a request.
		resp, err := server.EnqueueUserDataDeletion(
			iapContext("CONTENT_ADMIN"),
			&pb.EnqueueUserDataDeletionRequest{
				UserId: "test-user",
				Reason: "test-reason",
				Request: &pb.EnqueueUserDataDeletionRequest_ContentDeletion{
					ContentDeletion: &pb.ContentDeletionRequest{},
				},
			},
		)
		require.NoError(t, err)
		require.NotEmpty(t, resp.DeletionId)

		// Enqueue a different type of request.
		_, err = server.EnqueueUserDataDeletion(
			iapContext("AUTH_RW"),
			&pb.EnqueueUserDataDeletionRequest{
				UserId: "test-user",
				Reason: "test-reason",
				Request: &pb.EnqueueUserDataDeletionRequest_AccountDeletion{
					AccountDeletion: &pb.AccountDeletionRequest{},
				},
			},
		)
		require.Error(t, err)

		// Make sure the database has only one deletion.
		deletions, err := server.persistence.ListDeletions(context.Background(), 100)
		require.NoError(t, err)
		require.Len(t, deletions, 1)
	})
}

func TestGetDeletion(t *testing.T) {
	t.Run("auth", func(t *testing.T) {
		server, cleanup := testServer(t)
		defer cleanup()

		// Create a deletion.
		enqueueDeletionReq := &pb.EnqueueUserDataDeletionRequest{
			UserId: "test-user",
			Reason: "test-reason",
			Request: &pb.EnqueueUserDataDeletionRequest_ContentDeletion{
				ContentDeletion: &pb.ContentDeletionRequest{},
			},
		}
		enqueueDeletionResp, err := server.EnqueueUserDataDeletion(iapContext("CONTENT_ADMIN"), enqueueDeletionReq)
		require.NoError(t, err)
		require.NotEmpty(t, enqueueDeletionResp.DeletionId)

		req := &pb.GetDeletionRequest{DeletionId: enqueueDeletionResp.DeletionId}

		// Missing claims
		_, err = server.GetDeletion(context.Background(), req)
		require.Error(t, err)
		require.Equal(t, codes.PermissionDenied, status.Code(err))

		// Incorrect scope
		_, err = server.GetDeletion(iapContext("CONTENT_ADMIN"), req)
		require.Error(t, err)
		require.Equal(t, codes.PermissionDenied, status.Code(err))

		// Incorrect user
		_, err = server.GetDeletion(userContext("wrong-user", "PII_ADMIN"), req)
		require.Error(t, err)
		require.Equal(t, codes.PermissionDenied, status.Code(err))
	})

	t.Run("deletion exists", func(t *testing.T) {
		server, cleanup := testServer(t)
		defer cleanup()

		enqueueDeletionResp, err := server.EnqueueUserDataDeletion(
			iapContext("CONTENT_ADMIN"), &pb.EnqueueUserDataDeletionRequest{
				UserId: "test-user",
				Reason: "test-reason",
				Request: &pb.EnqueueUserDataDeletionRequest_ContentDeletion{
					ContentDeletion: &pb.ContentDeletionRequest{},
				},
			})
		require.NoError(t, err)
		require.NotEmpty(t, enqueueDeletionResp.DeletionId)

		resp, err := server.GetDeletion(
			iapContext("PII_ADMIN"), &pb.GetDeletionRequest{DeletionId: enqueueDeletionResp.DeletionId})
		require.NoError(t, err)
		require.NotNil(t, resp)

		checkDeletion(t, &pb.DeletionInfo{
			DeletionId: enqueueDeletionResp.DeletionId,
			Status:     pb.DeletionInfo_PENDING,
			Request: &pb.EnqueueUserDataDeletionRequest{
				UserId: "test-user",
				Reason: "test-reason",
				Request: &pb.EnqueueUserDataDeletionRequest_ContentDeletion{
					ContentDeletion: &pb.ContentDeletionRequest{},
				},
			},
			UserEmail: "<EMAIL>",
			CreatedAt: timestamppb.Now(),
		}, resp.DeletionInfo)

		checkTasks(t, []*pb.TaskInfo{
			{
				Status:  pb.TaskInfo_BLOCKED,
				Details: &pb.TaskDetails{Task: &pb.TaskDetails_VanguardExportDeleteRequestData{}},
			},
			{
				Status:  pb.TaskInfo_BLOCKED,
				Details: &pb.TaskDetails{Task: &pb.TaskDetails_VanguardExportDeleteBlobs{}},
			},
			{
				Status:  pb.TaskInfo_BLOCKED,
				Details: &pb.TaskDetails{Task: &pb.TaskDetails_ContentManagerDeleteBlobs{}},
			},
			{
				Status:  pb.TaskInfo_BLOCKED,
				Details: &pb.TaskDetails{Task: &pb.TaskDetails_RemoteAgentsDeleteData{}},
			},
		}, resp.TaskInfo)
	})

	t.Run("deletion does not exist", func(t *testing.T) {
		server, cleanup := testServer(t)
		defer cleanup()

		_, err := server.GetDeletion(
			iapContext("PII_ADMIN"), &pb.GetDeletionRequest{DeletionId: "non-existent-id"})
		require.Error(t, err)
		require.Equal(t, codes.NotFound, status.Code(err))
	})
}

func TestListDeletions(t *testing.T) {
	t.Run("auth", func(t *testing.T) {
		server, cleanup := testServer(t)
		defer cleanup()

		// Missing claims
		_, err := server.ListDeletions(context.Background(), &pb.ListDeletionsRequest{})
		require.Error(t, err)
		require.Equal(t, codes.PermissionDenied, status.Code(err))

		// Incorrect scope
		_, err = server.ListDeletions(iapContext("CONTENT_ADMIN"), &pb.ListDeletionsRequest{})
		require.Error(t, err)
		require.Equal(t, codes.PermissionDenied, status.Code(err))

		// User request
		_, err = server.ListDeletions(userContext("test-user", "PII_ADMIN"), &pb.ListDeletionsRequest{})
		require.Error(t, err)
		require.Equal(t, codes.PermissionDenied, status.Code(err))
	})

	t.Run("single deletion", func(t *testing.T) {
		server, cleanup := testServer(t)
		defer cleanup()

		// Create a deletion.
		enqueueDeletionResp, err := server.EnqueueUserDataDeletion(
			iapContext("CONTENT_ADMIN"), &pb.EnqueueUserDataDeletionRequest{
				UserId: "test-user",
				Reason: "test-reason",
				Request: &pb.EnqueueUserDataDeletionRequest_ContentDeletion{
					ContentDeletion: &pb.ContentDeletionRequest{},
				},
			},
		)
		require.NoError(t, err)
		require.NotEmpty(t, enqueueDeletionResp.DeletionId)

		// List it.
		resp, err := server.ListDeletions(iapContext("PII_ADMIN"), &pb.ListDeletionsRequest{})
		require.NoError(t, err)
		require.Len(t, resp.DeletionInfo, 1)
		checkDeletion(t, &pb.DeletionInfo{
			DeletionId: enqueueDeletionResp.DeletionId,
			Status:     pb.DeletionInfo_PENDING,
			Request: &pb.EnqueueUserDataDeletionRequest{
				UserId: "test-user",
				Reason: "test-reason",
				Request: &pb.EnqueueUserDataDeletionRequest_ContentDeletion{
					ContentDeletion: &pb.ContentDeletionRequest{},
				},
			},
			UserEmail: "<EMAIL>",
			CreatedAt: timestamppb.Now(),
		}, resp.DeletionInfo[0])
	})

	t.Run("page size and ordering", func(t *testing.T) {
		server, cleanup := testServer(t)
		defer cleanup()

		// Create 15 deletions.
		for i := 0; i < 15; i++ {
			_, err := server.EnqueueUserDataDeletion(
				iapContext("CONTENT_ADMIN"), &pb.EnqueueUserDataDeletionRequest{
					UserId: fmt.Sprintf("test-user-%d", i),
					Reason: "test-reason",
					Request: &pb.EnqueueUserDataDeletionRequest_ContentDeletion{
						ContentDeletion: &pb.ContentDeletionRequest{},
					},
				},
			)
			require.NoError(t, err)
		}

		// List only 10.
		resp, err := server.ListDeletions(iapContext("PII_ADMIN"), &pb.ListDeletionsRequest{PageSize: 10})
		require.NoError(t, err)
		require.Len(t, resp.DeletionInfo, 10)
		// Make sure results are in reverse chronological order.
		for i := 1; i < len(resp.DeletionInfo); i++ {
			require.True(t, resp.DeletionInfo[i-1].CreatedAt.AsTime().After(resp.DeletionInfo[i].CreatedAt.AsTime()))
		}
	})
}
