package main

import (
	"context"
	"fmt"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging/audit"
	authclient "github.com/augmentcode/augment/services/auth/central/auth_client"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	pb "github.com/augmentcode/augment/services/shredder/proto"
	tokenexchangeproto "github.com/augmentcode/augment/services/token_exchange/proto"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
)

const (
	DefaultPageSize = 100
)

type shredderServer struct {
	pb.UnimplementedShredderServer

	featureFlagHandle featureflags.FeatureFlagHandle
	auditLogger       *audit.AuditLogger
	persistence       *ShredderPersistence
	authClient        authclient.AuthClient
}

func NewShredderServer(
	featureFlagHandle featureflags.FeatureFlagHandle,
	auditLogger *audit.AuditLogger,
	persistence *ShredderPersistence,
	authClient authclient.AuthClient,
) *shredderServer {
	return &shredderServer{
		featureFlagHandle: featureFlagHandle,
		auditLogger:       auditLogger,
		persistence:       persistence,
		authClient:        authClient,
	}
}

func (s *shredderServer) EnqueueUserDataDeletion(
	ctx context.Context, req *pb.EnqueueUserDataDeletionRequest,
) (*pb.EnqueueUserDataDeletionResponse, error) {
	if req.UserId == "" || req.Reason == "" {
		return nil, status.Error(codes.InvalidArgument, "user_id and reason are required")
	}

	var requiredScope tokenexchangeproto.Scope
	switch req.Request.(type) {
	case *pb.EnqueueUserDataDeletionRequest_AccountDeletion:
		requiredScope = tokenexchangeproto.Scope_AUTH_RW
	case *pb.EnqueueUserDataDeletionRequest_ContentDeletion:
		requiredScope = tokenexchangeproto.Scope_CONTENT_ADMIN
	case *pb.EnqueueUserDataDeletionRequest_GdprCcpaDeletion:
		requiredScope = tokenexchangeproto.Scope_PII_ADMIN
	default:
		return nil, status.Error(codes.InvalidArgument, "invalid request type")
	}
	authClaims, err := userAuthCheck(ctx, requiredScope, req.UserId)
	if err != nil {
		return nil, err
	}

	// Fetch user from auth-central to get their email.
	requestCtx, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		log.Ctx(ctx).Err(err).Msg("Failed to get request context")
		return nil, fmt.Errorf("Failed to get request context: %v", err)
	}
	user, err := s.authClient.GetUser(ctx, requestCtx, req.UserId, nil)
	if err != nil {
		log.Ctx(ctx).Err(err).Msg("Failed to get user")
		return nil, fmt.Errorf("Failed to get user: %v", err)
	} else if user == nil {
		log.Ctx(ctx).Info().Msgf("User %s not found", req.UserId)
		return nil, status.Error(codes.NotFound, "User not found")
	}

	deletion := &pb.DeletionInfo{
		DeletionId: uuid.New().String(),
		Status:     pb.DeletionInfo_PENDING,
		CreatedAt:  timestamppb.Now(),
		Request:    req,
		UserEmail:  user.Email,
	}

	tasks, err := getTasksForDeletion(req)
	if err != nil {
		log.Ctx(ctx).Err(err).Msg("Failed to get tasks for deletion")
		return nil, err
	}

	// Write the audit log before persisting anything.
	s.auditLogger.WriteAuditLog(
		authClaims.OpaqueUserID,
		authClaims.OpaqueUserIDType,
		authClaims.TenantName,
		fmt.Sprintf(
			"EnqueueUserDataDeletion request (type: %s) received for user %s",
			deletionRequestType(deletion), req.UserId),
	)

	deletionID, err := s.persistence.CreateDeletion(ctx, deletion, tasks)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to create deletion")
		return nil, status.Errorf(codes.Internal, "Failed to create deletion")
	}

	return &pb.EnqueueUserDataDeletionResponse{
		DeletionId: deletionID,
	}, nil
}

func (s *shredderServer) GetDeletion(
	ctx context.Context, req *pb.GetDeletionRequest,
) (*pb.GetDeletionResponse, error) {
	// Check for PII_ADMIN scope. We can't check the user yet because we don't know it.
	// We require PII_ADMIN because this endpoint can return user emails for users who have requested
	// GDPR/CCPA deletion.
	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		log.Ctx(ctx).Error().Msg("Failed to get auth claims from context")
		return nil, status.Error(codes.PermissionDenied, "Invalid context")
	}
	if !authClaims.HasScope(tokenexchangeproto.Scope_PII_ADMIN) {
		log.Ctx(ctx).Error().Msgf("Auth claims do not have required scope PII_ADMIN")
		return nil, status.Error(codes.PermissionDenied, "Access denied")
	}

	deletion, tasks, err := s.persistence.GetDeletionAndTasks(ctx, req.DeletionId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get deletion")
		return nil, status.Errorf(codes.Internal, "Failed to get deletion")
	} else if deletion == nil {
		return nil, status.Errorf(codes.NotFound, "Deletion not found")
	}

	// Check that the user is allowed to access this deletion before returning.
	_, err = userAuthCheck(ctx, tokenexchangeproto.Scope_PII_ADMIN, deletion.GetRequest().GetUserId())
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to check user auth")
		return nil, err
	}

	s.auditLogger.WriteAuditLog(
		authClaims.OpaqueUserID,
		authClaims.OpaqueUserIDType,
		authClaims.TenantName,
		fmt.Sprintf("GetDeletion request received for deletion %s", req.DeletionId),
	)

	return &pb.GetDeletionResponse{
		DeletionInfo: deletion,
		TaskInfo:     tasks,
	}, nil
}

func (s *shredderServer) ListDeletions(
	ctx context.Context, req *pb.ListDeletionsRequest,
) (*pb.ListDeletionsResponse, error) {
	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		log.Ctx(ctx).Error().Msg("Failed to get auth claims from context")
		return nil, status.Error(codes.PermissionDenied, "Invalid context")
	} else if !authClaims.HasScope(tokenexchangeproto.Scope_PII_ADMIN) {
		log.Ctx(ctx).Error().Msgf("Auth claims do not have required scope PII_ADMIN")
		return nil, status.Error(codes.PermissionDenied, "Access denied")
	} else if !authClaims.AllowsAllTenants() {
		log.Ctx(ctx).Error().Msgf("Auth claims do not give access to all tenants")
		return nil, status.Error(codes.PermissionDenied, "Access denied")
	}

	s.auditLogger.WriteAuditLog(
		authClaims.OpaqueUserID,
		authClaims.OpaqueUserIDType,
		authClaims.TenantName,
		"ListDeletions request received",
	)

	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = DefaultPageSize
	}

	deletions, err := s.persistence.ListDeletions(ctx, pageSize)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to list deletions")
		return nil, status.Errorf(codes.Internal, "Failed to list deletions")
	}

	return &pb.ListDeletionsResponse{
		DeletionInfo: deletions,
	}, nil
}

// Get the tasks that should be executed for the given deletion request. Tasks are returned in the
// order that they should be executed.
func getTasksForDeletion(req *pb.EnqueueUserDataDeletionRequest) ([]*pb.TaskInfo, error) {
	switch req.Request.(type) {
	case *pb.EnqueueUserDataDeletionRequest_AccountDeletion:
		return []*pb.TaskInfo{
			newTask(&pb.TaskDetails{Task: &pb.TaskDetails_ContentManagerDeleteBlobs{}}),
			newTask(&pb.TaskDetails{Task: &pb.TaskDetails_RemoteAgentsDeleteData{}}),
			newTask(&pb.TaskDetails{Task: &pb.TaskDetails_SettingsDeleteUserSettings{}}),
			newTask(&pb.TaskDetails{Task: &pb.TaskDetails_AuthCentralDeleteAccount{}}),
		}, nil
	case *pb.EnqueueUserDataDeletionRequest_ContentDeletion:
		return []*pb.TaskInfo{
			newTask(&pb.TaskDetails{Task: &pb.TaskDetails_VanguardExportDeleteRequestData{}}),
			newTask(&pb.TaskDetails{Task: &pb.TaskDetails_VanguardExportDeleteBlobs{}}),
			newTask(&pb.TaskDetails{Task: &pb.TaskDetails_ContentManagerDeleteBlobs{}}),
			newTask(&pb.TaskDetails{Task: &pb.TaskDetails_RemoteAgentsDeleteData{}}),
		}, nil
	case *pb.EnqueueUserDataDeletionRequest_GdprCcpaDeletion:
		return []*pb.TaskInfo{
			newTask(&pb.TaskDetails{Task: &pb.TaskDetails_VanguardExportDeleteRequestData{}}),
			newTask(&pb.TaskDetails{Task: &pb.TaskDetails_VanguardExportDeleteBlobs{}}),
			newTask(&pb.TaskDetails{Task: &pb.TaskDetails_ContentManagerDeleteBlobs{}}),
			newTask(&pb.TaskDetails{Task: &pb.TaskDetails_RemoteAgentsDeleteData{}}),
			newTask(&pb.TaskDetails{Task: &pb.TaskDetails_SettingsDeleteUserSettings{}}),
			newTask(&pb.TaskDetails{Task: &pb.TaskDetails_AuthCentralDeleteAccount{}}),
			newTask(&pb.TaskDetails{Task: &pb.TaskDetails_AnalyticsForgetUser{}}),
			newTask(&pb.TaskDetails{Task: &pb.TaskDetails_AuthCentralForgetUser{}}),
		}, nil
	default:
		return nil, status.Error(codes.InvalidArgument, "invalid request type")
	}
}

func newTask(details *pb.TaskDetails) *pb.TaskInfo {
	return &pb.TaskInfo{
		TaskId: uuid.New().String(),
		// Tasks start as BLOCKED and are transitioned to PENDING appropriately by the async processing
		// loop.
		Status:  pb.TaskInfo_BLOCKED,
		Details: details,
	}
}

// Auth check for requests that operate on a single user.
func userAuthCheck(
	ctx context.Context, requiredScope tokenexchangeproto.Scope, userID string,
) (*auth.AugmentClaims, error) {
	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		log.Ctx(ctx).Error().Msg("Failed to get auth claims from context")
		return nil, status.Error(codes.PermissionDenied, "Invalid context")
	}

	// For non-IAP calls, make sure the user id from the request matches the token.
	_, isIapUser := authClaims.GetIapEmail()
	if !isIapUser && authClaims.OpaqueUserID != userID {
		log.Ctx(ctx).Error().Msgf("Auth claims user ID %s does not match requested user ID %s", authClaims.UserID, userID)
		return authClaims, status.Error(codes.PermissionDenied, "Access denied")
	}

	if !authClaims.HasScope(requiredScope) {
		log.Ctx(ctx).Error().Msgf("Auth claims do not have required scope %s", requiredScope)
		return authClaims, status.Error(codes.PermissionDenied, "Access denied")
	}

	return authClaims, nil
}
