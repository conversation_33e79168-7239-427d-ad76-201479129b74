local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
{
  deployment: [
    {
      name: 'shredder-spanner-database',
      priority: 100,  // We want tables to be created/modified before shredder deploys.
      kubecfg: {
        target: '//services/shredder/server:kubecfg_spanner_database',
        task: [
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'STAGING',
            namespace: 'central-staging',
          },
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'PROD',
            namespace: 'central',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['jacqueline', 'dirk'],
          slack_channel: '#team-growth',
        },
      },
    },
  ],
}
