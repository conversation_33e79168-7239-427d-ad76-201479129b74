import { LoaderFunctionArgs } from "@remix-run/router";
import { Code, ConnectError } from "@connectrpc/connect";
import { getTokenExchangeClient } from "../.server/grpc/token-exchange";
import { getTeamManagementClient } from "../.server/grpc/team-management";
import { connectCodeToHttpStatus } from "../utils/grpc-utils";
import { Scope } from "~services/token_exchange/token_exchange_pb";
import { Duration } from "@bufbuild/protobuf";
import type { ErrorResponse, SubscriptionApiResponse } from "../schemas/users";
import { logger } from "@augment-internal/logging";
import type {
  GetUserOrbCreditsInfoResponse,
  GetUserOrbSubscriptionInfoResponse,
} from "~services/auth/central/server/auth_pb";

/**
 * Builds the subscription part of the response, handling different subscription states
 */
function buildSubscriptionResponse(
  subscriptionInfo: GetUserOrbSubscriptionInfoResponse,
): SubscriptionApiResponse["subscription"] {
  // Handle different subscription states
  if (subscriptionInfo.orbSubscriptionInfo.case === "pendingSubscription") {
    return { status: "pending" };
  }

  if (subscriptionInfo.orbSubscriptionInfo.case === "nonexistentSubscription") {
    return { status: "nonexistent" };
  }

  if (subscriptionInfo.orbSubscriptionInfo.case !== "subscription") {
    return { status: "unknown" };
  }

  const subscription = subscriptionInfo.orbSubscriptionInfo.value;

  return {
    status: subscription.subscriptionStatus,
    orbCustomerId: subscription.orbCustomerId,
    orbSubscriptionId: subscription.orbSubscriptionId,
    externalPlanId: subscription.externalPlanId,
    billingPeriodEndDate: subscription.billingPeriodEndDateIso,
    nextBillingCycleAmount: subscription.nextBillingCycleAmount,
    seats: subscription.seats,
  };
}

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const { userId, tenantId } = params;

  if (!userId || !tenantId) {
    const errorResponse: ErrorResponse = {
      error: "User ID and Tenant ID are required",
    };
    return Response.json(errorResponse, { status: 400 });
  }

  const IAPJWT = request.headers.get("X-Goog-IAP-JWT-Assertion") ?? "";

  if (!IAPJWT) {
    const errorResponse: ErrorResponse = {
      error: "Authentication required",
    };
    return Response.json(errorResponse, { status: 401 });
  }

  // Get signed token for authentication
  let signedToken: string;
  try {
    const tokenResponse =
      await getTokenExchangeClient().getSignedTokenForIAPToken({
        tenantId: tenantId,
        iapToken: IAPJWT,
        scopes: [Scope.AUTH_R], // Read access for subscription info
        expiration: new Duration({ seconds: BigInt(60 * 60) }), // 1 hour
      });
    signedToken = tokenResponse.signedToken;
  } catch (error) {
    // Handle errors from token exchange
    if (error instanceof ConnectError) {
      logger.error(`Token exchange error: ${error.code} - ${error.message}`);

      const errorResponse: ErrorResponse = {
        error: error.message,
      };

      // Return 401 for unauthenticated, 403 for permission denied, 500 for all other errors
      let status = 500;
      if (error.code === Code.Unauthenticated) {
        status = 401;
      } else if (error.code === Code.PermissionDenied) {
        status = 403;
      }

      return Response.json(errorResponse, { status });
    }

    // For non-ConnectError errors, return a generic server error
    console.error(`Unexpected error in token exchange: ${error}`);
    const errorResponse: ErrorResponse = {
      error: "An unexpected error occurred during authentication",
    };
    return Response.json(errorResponse, { status: 500 });
  }

  try {
    const teamManagementClient = getTeamManagementClient();
    const authHeaders = {
      headers: {
        Authorization: `Bearer ${signedToken}`,
      },
    };

    // Get both the subscription info and credits info
    const [creditsInfo, subscriptionInfo]: [
      GetUserOrbCreditsInfoResponse,
      GetUserOrbSubscriptionInfoResponse,
    ] = await Promise.all([
      teamManagementClient.getUserOrbCreditsInfo({ userId }, authHeaders),
      teamManagementClient.getUserOrbSubscriptionInfo({ userId }, authHeaders),
    ]);

    // Build the response
    const response: SubscriptionApiResponse = {
      credits: {
        usageUnitsAvailable: creditsInfo.usageUnitsAvailable,
        usageUnitsUsedThisBillingCycle:
          creditsInfo.usageUnitsUsedThisBillingCycle,
      },
      subscription: buildSubscriptionResponse(subscriptionInfo),
    };

    return Response.json(response, { status: 200 });
  } catch (error) {
    // Handle errors from gRPC calls or data transformation
    if (error instanceof ConnectError) {
      logger.error(`gRPC error: ${error.code} - ${error.message}`, {
        userId,
        tenantId,
        errorCode: error.code,
      });

      const errorResponse: ErrorResponse = {
        error: error.message,
      };
      return Response.json(errorResponse, {
        status: connectCodeToHttpStatus(error.code),
      });
    }

    // For non-ConnectError errors, return a generic server error
    logger.error(`Unexpected error fetching subscription info: ${error}`, {
      userId,
      tenantId,
    });
    const errorResponse: ErrorResponse = {
      error: "Failed to fetch subscription information",
    };
    return Response.json(errorResponse, { status: 500 });
  }
};
