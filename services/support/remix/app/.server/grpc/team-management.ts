import { createGrpcTransport } from "@connectrpc/connect-node";
import { type CallOptions, createClient } from "@connectrpc/connect";
import type { PartialMessage } from "@bufbuild/protobuf";

import { Config } from "../config";
// eslint-disable-next-line import/no-unresolved
import { TeamManagementService } from "~services/auth/central/server/auth_connect";
import type {
  CancelSubscriptionRequest,
  CancelSubscriptionResponse,
  GetUserOrbCreditsInfoRequest,
  GetUserOrbCreditsInfoResponse,
  GetUserOrbSubscriptionInfoRequest,
  GetUserOrbSubscriptionInfoResponse,
} from "~services/auth/central/server/auth_pb";

class TeamManagementClient {
  constructor(
    private readonly client = createClient(
      TeamManagementService,
      createGrpcTransport(
        Config.createGrpcTransportOptions(Config.AUTH_CENTRAL_ENDPOINT || ""),
      ),
    ),
  ) {
    this.cancelSubscription = this.client.cancelSubscription.bind(this.client);
    this.getUserOrbCreditsInfo = this.client.getUserOrbCreditsInfo.bind(
      this.client,
    );
    this.getUserOrbSubscriptionInfo =
      this.client.getUserOrbSubscriptionInfo.bind(this.client);
  }

  cancelSubscription: (
    request: PartialMessage<CancelSubscriptionRequest>,
    options?: CallOptions,
  ) => Promise<CancelSubscriptionResponse>;

  getUserOrbCreditsInfo: (
    request: PartialMessage<GetUserOrbCreditsInfoRequest>,
    options?: CallOptions,
  ) => Promise<GetUserOrbCreditsInfoResponse>;

  getUserOrbSubscriptionInfo: (
    request: PartialMessage<GetUserOrbSubscriptionInfoRequest>,
    options?: CallOptions,
  ) => Promise<GetUserOrbSubscriptionInfoResponse>;
}

let client: TeamManagementClient;
/**
 * lazy initializes the team management client. This is to avoid creating a new client for each request.
 * It also makes testing easier by not initializing on module load.
 * @returns TeamManagementClient
 */
export function getTeamManagementClient() {
  if (!client) {
    if (!Config.AUTH_CENTRAL_ENDPOINT) {
      throw new Error("AUTH_CENTRAL_ENDPOINT is not defined");
    }
    client = new TeamManagementClient();
    console.info(
      `Created team management client: ${Config.AUTH_CENTRAL_ENDPOINT}`,
    );
  }
  return client;
}
