import { z } from "zod";

// Schema for promotion id validation
// Allows alphanumeric characters, underscores, and hyphens
// Max length of 255 characters
export const PromotionIdSchema = z
  .string()
  .min(1, "Promotion id is required")
  .max(255, "Promotion id must be 255 characters or less")
  .regex(
    /^[a-zA-Z0-9_-]+$/,
    "Promotion id can only contain letters, numbers, underscores, and hyphens",
  );

export type PromotionId = z.infer<typeof PromotionIdSchema>;

// Schema for promotion eligibility response
export const PromotionEligibilityResponseSchema = z.object({
  promotionStatus: z.number(), // PromotionStatus enum value
});

export type PromotionEligibilityResponse = z.infer<
  typeof PromotionEligibilityResponseSchema
>;

// Schema for promotion processing request
export const ProcessPromotionRequestSchema = z.object({
  promotionId: PromotionIdSchema,
  file: z.instanceof(File),
});

export type ProcessPromotionRequest = z.infer<
  typeof ProcessPromotionRequestSchema
>;

// Schema for promotion processing response
export const ProcessPromotionResponseSchema = z.object({
  success: z.boolean(),
  message: z.string().optional(),
});

export type ProcessPromotionResponse = z.infer<
  typeof ProcessPromotionResponseSchema
>;
