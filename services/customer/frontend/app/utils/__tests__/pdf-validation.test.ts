import { describe, it, expect, vi, beforeEach } from "vitest";

// Mock PDF.js at the top level
const mockGetDocument = vi.fn();
const mockPdf = {
  numPages: 2,
  getPage: vi.fn(),
};
const mockPage = {
  getTextContent: vi.fn(),
  cleanup: vi.fn(),
};

vi.mock("pdfjs-dist/legacy/build/pdf.mjs", () => ({
  getDocument: mockGetDocument,
}));

import {
  isPDFFile,
  isValidFileSize,
  validatePDFFile,
  extractEmailsFromText,
  validatePDFContent,
  extractPDFText,
} from "../pdf-validation";

describe("PDF Validation Utils", () => {
  // Helper to create mock File objects
  const createMockFile = (
    name: string,
    type: string,
    size: number = 1024,
  ): File => {
    // Create content that matches the desired size
    const content = new Array(size).fill("a").join("");
    const file = new File([content], name, { type, lastModified: Date.now() });

    // Add arrayBuffer method for PDF tests
    (file as any).arrayBuffer = vi
      .fn()
      .mockResolvedValue(new ArrayBuffer(size));

    return file;
  };

  describe("isPDFFile", () => {
    it("should return true for valid PDF MIME types", () => {
      const validMimeTypes = [
        "application/pdf",
        "application/x-pdf",
        "application/acrobat",
        "applications/vnd.pdf",
        "text/pdf",
        "text/x-pdf",
      ];

      validMimeTypes.forEach((mimeType) => {
        const file = createMockFile("test.pdf", mimeType);
        expect(isPDFFile(file)).toBe(true);
      });
    });

    it("should return true for .pdf extension even with invalid MIME type", () => {
      const file = createMockFile("test.pdf", "application/octet-stream");
      expect(isPDFFile(file)).toBe(true);
    });

    it("should return false for non-PDF files", () => {
      const invalidFiles = [
        createMockFile("test.txt", "text/plain"),
        createMockFile("test.jpg", "image/jpeg"),
        createMockFile("test.doc", "application/msword"),
        createMockFile(
          "test.xlsx",
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        ),
      ];

      invalidFiles.forEach((file) => {
        expect(isPDFFile(file)).toBe(false);
      });
    });

    it("should handle case insensitive extensions", () => {
      const file = createMockFile("test.PDF", "application/octet-stream");
      expect(isPDFFile(file)).toBe(true);
    });
  });

  describe("isValidFileSize", () => {
    it("should return true for files within size limit", () => {
      const file = createMockFile(
        "test.pdf",
        "application/pdf",
        5 * 1024 * 1024,
      ); // 5MB
      expect(isValidFileSize(file, 10)).toBe(true);
    });

    it("should return false for files exceeding size limit", () => {
      const file = createMockFile(
        "test.pdf",
        "application/pdf",
        15 * 1024 * 1024,
      ); // 15MB
      expect(isValidFileSize(file, 10)).toBe(false);
    });

    it("should use default 10MB limit when not specified", () => {
      const file = createMockFile(
        "test.pdf",
        "application/pdf",
        5 * 1024 * 1024,
      ); // 5MB
      expect(isValidFileSize(file)).toBe(true);
    });

    it("should return true for files exactly at the limit", () => {
      const file = createMockFile(
        "test.pdf",
        "application/pdf",
        10 * 1024 * 1024,
      ); // 10MB
      expect(isValidFileSize(file, 10)).toBe(true);
    });
  });

  describe("validatePDFFile", () => {
    it("should return valid for correct PDF files", () => {
      const file = createMockFile(
        "test.pdf",
        "application/pdf",
        5 * 1024 * 1024,
      );
      const result = validatePDFFile(file);
      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it("should return invalid for non-PDF files", () => {
      const file = createMockFile("test.txt", "text/plain", 1024);
      const result = validatePDFFile(file);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain("Invalid file type");
      expect(result.error).toContain("test.txt");
    });

    it("should return invalid for oversized files", () => {
      const file = createMockFile(
        "test.pdf",
        "application/pdf",
        15 * 1024 * 1024,
      );
      const result = validatePDFFile(file, 10);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain("File too large");
      expect(result.error).toContain("test.pdf");
    });
  });

  describe("extractEmailsFromText", () => {
    it("should extract valid email addresses", () => {
      const text =
        "Contact <NAME_EMAIL> or <EMAIL> for help.";
      const emails = extractEmailsFromText(text);
      expect(emails).toEqual(["<EMAIL>", "<EMAIL>"]);
    });

    it("should handle duplicate emails", () => {
      const text = "Email: <EMAIL> <NAME_EMAIL>";
      const emails = extractEmailsFromText(text);
      expect(emails).toEqual(["<EMAIL>"]);
    });

    it("should return empty array for text without emails", () => {
      const text = "This text has no email addresses in it.";
      const emails = extractEmailsFromText(text);
      expect(emails).toEqual([]);
    });

    it("should handle case insensitive emails", () => {
      const text = "Contact <EMAIL> for support";
      const emails = extractEmailsFromText(text);
      expect(emails).toEqual(["<EMAIL>"]);
    });

    it("should extract emails with various formats", () => {
      const text =
        "Emails: <EMAIL> <EMAIL> <EMAIL>";
      const emails = extractEmailsFromText(text);
      expect(emails).toEqual([
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
      ]);
    });

    it("should handle emails with special characters", () => {
      const text = "Contact: <EMAIL> <EMAIL>";
      const emails = extractEmailsFromText(text);
      expect(emails).toEqual(["<EMAIL>", "<EMAIL>"]);
    });

    it("should handle malformed email-like strings", () => {
      const text = "Not emails: @example.com, user@, user@domain, @domain.com";
      const emails = extractEmailsFromText(text);
      expect(emails).toEqual([]);
    });

    it("should handle empty and whitespace-only text", () => {
      expect(extractEmailsFromText("")).toEqual([]);
      expect(extractEmailsFromText("   \n\t  ")).toEqual([]);
    });
  });

  describe("extractPDFText", () => {
    beforeEach(() => {
      vi.clearAllMocks();
      // Reset the mock objects
      mockPdf.numPages = 2;
      mockPdf.getPage = vi.fn();
      mockPage.getTextContent = vi.fn();
      mockPage.cleanup = vi.fn();
    });

    it("should extract text from PDF successfully", async () => {
      const mockTextContent = {
        items: [
          { str: "Hello" },
          { str: "World" },
          { str: "<EMAIL>" },
        ],
      };

      mockGetDocument.mockReturnValue({
        promise: Promise.resolve(mockPdf),
      });
      mockPdf.getPage.mockResolvedValue(mockPage);
      mockPage.getTextContent.mockResolvedValue(mockTextContent);

      const file = createMockFile("test.pdf", "application/pdf", 1024);

      const result = await extractPDFText(file);

      expect(result).toContain("<NAME_EMAIL>");
      expect(mockGetDocument).toHaveBeenCalledWith({
        data: expect.any(ArrayBuffer),
        cMapUrl: undefined,
        cMapPacked: false,
        standardFontDataUrl: undefined,
        useWorkerFetch: false,
        isEvalSupported: false,
        useSystemFonts: true,
        disableFontFace: true,
        disableRange: false,
        disableStream: false,
        verbosity: 0,
      });
      expect(mockPdf.getPage).toHaveBeenCalledTimes(2);
      expect(mockPage.cleanup).toHaveBeenCalledTimes(2);
    });

    it("should throw error when PDF has no text content", async () => {
      const mockTextContent = {
        items: [],
      };

      // Set up for single page with no content
      mockPdf.numPages = 1;
      mockGetDocument.mockReturnValue({
        promise: Promise.resolve(mockPdf),
      });
      mockPdf.getPage.mockResolvedValue(mockPage);
      mockPage.getTextContent.mockResolvedValue(mockTextContent);

      const file = createMockFile("test.pdf", "application/pdf", 1024);

      await expect(extractPDFText(file)).rejects.toThrow(
        "No text content found in PDF",
      );
    });
  });

  describe("validatePDFContent", () => {
    beforeEach(() => {
      vi.clearAllMocks();
    });

    it("should validate PDF content successfully when user email is found", async () => {
      // Set up the PDF.js mock to return the text we want for this test
      const mockTextContent = {
        items: [
          { str: "Invoice" },
          { str: "for" },
          { str: "<EMAIL>" },
          { str: "from" },
          { str: "<EMAIL>" },
          { str: "Total:" },
          { str: "$50.00" },
        ],
      };

      mockPdf.numPages = 1;
      mockGetDocument.mockReturnValue({
        promise: Promise.resolve(mockPdf),
      });
      mockPdf.getPage.mockResolvedValue(mockPage);
      mockPage.getTextContent.mockResolvedValue(mockTextContent);

      const file = createMockFile("invoice.pdf", "application/pdf", 1024);
      const options = {
        userEmail: "<EMAIL>",
        requiredSenderEmail: "<EMAIL>",
      };

      const result = await validatePDFContent(file, options);

      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it("should fail validation when user email is not found", async () => {
      // Set up PDF.js mock to return text without the user's email
      const mockTextContent = {
        items: [
          { str: "Invoice" },
          { str: "for" },
          { str: "<EMAIL>" },
          { str: "from" },
          { str: "<EMAIL>" },
          { str: "Total:" },
          { str: "$50.00" },
        ],
      };

      mockPdf.numPages = 1;
      mockGetDocument.mockReturnValue({
        promise: Promise.resolve(mockPdf),
      });
      mockPdf.getPage.mockResolvedValue(mockPage);
      mockPage.getTextContent.mockResolvedValue(mockTextContent);

      const file = createMockFile("invoice.pdf", "application/pdf", 1024);
      const options = {
        userEmail: "<EMAIL>",
        requiredSenderEmail: "<EMAIL>",
      };

      const result = await validatePDFContent(file, options);

      expect(result.isValid).toBe(false);
      expect(result.error).toBe("Requested email not found in invoice.");
    });

    it("should fail validation when required sender email is not found", async () => {
      // Set up PDF.js mock to return text without the required sender email
      const mockTextContent = {
        items: [
          { str: "Invoice" },
          { str: "for" },
          { str: "<EMAIL>" },
          { str: "from" },
          { str: "<EMAIL>" },
          { str: "Total:" },
          { str: "$50.00" },
        ],
      };

      mockPdf.numPages = 1;
      mockGetDocument.mockReturnValue({
        promise: Promise.resolve(mockPdf),
      });
      mockPdf.getPage.mockResolvedValue(mockPage);
      mockPage.getTextContent.mockResolvedValue(mockTextContent);

      const file = createMockFile("invoice.pdf", "application/pdf", 1024);
      const options = {
        userEmail: "<EMAIL>",
        requiredSenderEmail: "<EMAIL>",
      };

      const result = await validatePDFContent(file, options);

      expect(result.isValid).toBe(false);
      expect(result.error).toBe(
        "<NAME_EMAIL> not found.",
      );
    });

    it("should validate successfully without required sender email", async () => {
      // Set up PDF.js mock to return text with user email but any sender email
      const mockTextContent = {
        items: [
          { str: "Invoice" },
          { str: "for" },
          { str: "<EMAIL>" },
          { str: "from" },
          { str: "<EMAIL>" },
          { str: "Total:" },
          { str: "$50.00" },
        ],
      };

      mockPdf.numPages = 1;
      mockGetDocument.mockReturnValue({
        promise: Promise.resolve(mockPdf),
      });
      mockPdf.getPage.mockResolvedValue(mockPage);
      mockPage.getTextContent.mockResolvedValue(mockTextContent);

      const file = createMockFile("invoice.pdf", "application/pdf", 1024);
      const options = {
        userEmail: "<EMAIL>",
      };

      const result = await validatePDFContent(file, options);

      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it("should handle PDF extraction errors gracefully", async () => {
      // Set up PDF.js mock to throw an error
      mockGetDocument.mockReturnValue({
        promise: Promise.reject(new Error("PDF parsing failed")),
      });

      const file = createMockFile("invalid.pdf", "application/pdf", 1024);
      const options = {
        userEmail: "<EMAIL>",
      };

      const result = await validatePDFContent(file, options);

      expect(result.isValid).toBe(false);
      expect(result.error).toBe(
        "Failed to validate PDF content: PDF parsing failed",
      );
    });

    it("should handle case insensitive email matching and ignore punctuation before/after email", async () => {
      // Set up PDF.js mock to return text with uppercase emails
      const mockTextContent = {
        items: [
          { str: "Invoice" },
          { str: "for" },
          { str: "..<EMAIL>," },
          { str: "from" },
          { str: "'<EMAIL>,'" },
          { str: "Total:" },
          { str: "$50.00" },
        ],
      };

      mockPdf.numPages = 1;
      mockGetDocument.mockReturnValue({
        promise: Promise.resolve(mockPdf),
      });
      mockPdf.getPage.mockResolvedValue(mockPage);
      mockPage.getTextContent.mockResolvedValue(mockTextContent);

      const file = createMockFile("invoice.pdf", "application/pdf", 1024);
      const options = {
        userEmail: "<EMAIL>",
        requiredSenderEmail: "<EMAIL>",
      };

      const result = await validatePDFContent(file, options);

      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });
  });
});
