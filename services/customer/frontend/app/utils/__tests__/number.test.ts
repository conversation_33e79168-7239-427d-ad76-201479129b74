import { describe, it, expect } from "vitest";
import { formatFileSize } from "../number";

describe("formatFileSize", () => {
  it("should format bytes correctly", () => {
    expect(formatFileSize(0)).toBe("0 Bytes");
    expect(formatFileSize(1024)).toBe("1 KB");
    expect(formatFileSize(1024 * 1024)).toBe("1 MB");
    expect(formatFileSize(1024 * 1024 * 1024)).toBe("1 GB");
  });

  it("should handle decimal values", () => {
    expect(formatFileSize(1536)).toBe("1.5 KB");
    expect(formatFileSize(2.5 * 1024 * 1024)).toBe("2.5 MB");
  });

  it("should handle small values", () => {
    expect(formatFileSize(512)).toBe("512 Bytes");
    expect(formatFileSize(1)).toBe("1 Bytes");
  });

  it("should handle non-round values", () => {
    expect(formatFileSize(1024 + 512)).toBe("1.5 KB");
    expect(formatFileSize(1024 * 1024 + 512 * 1024)).toBe("1.5 MB");

    expect(formatFileSize(1024 - 100)).toBe("924 Bytes");
    expect(formatFileSize(1024 * 1024 - 5)).toBe("1024 KB");

    expect(formatFileSize(123456789)).toBe("117.74 MB");
    expect(formatFileSize(9876543210)).toBe("9.2 GB");

    expect(formatFileSize(1.75 * 1024 * 1024 * 1024)).toBe("1.75 GB");

    expect(formatFileSize(1.5)).toBe("1.5 Bytes");
  });
});
