/**
 * PDF validation utilities for file upload components
 */

export interface PDFValidationResult {
  isValid: boolean;
  error?: string;
}

export interface PDFContentValidationResult {
  isValid: boolean;
  error?: string;
}

export interface PDFContentValidationOptions {
  userEmail: string;
  requiredSenderEmail?: string;
}

/**
 * Validates if a file is a PDF based on MIME type and file extension
 */
export function isPDFFile(file: File): boolean {
  // Check MIME type - PDF files can have different MIME types
  const validMimeTypes = [
    "application/pdf",
    "application/x-pdf",
    "application/acrobat",
    "applications/vnd.pdf",
    "text/pdf",
    "text/x-pdf",
  ];

  // Check file extension as fallback
  const fileName = file.name.toLowerCase();
  const hasValidExtension = fileName.endsWith(".pdf");

  // Accept if either MIME type is valid OR extension is .pdf
  return validMimeTypes.includes(file.type.toLowerCase()) || hasValidExtension;
}

/**
 * Validates if a file size is within the allowed limit
 */
export function isValidFileSize(file: File, maxSizeMB: number = 10): boolean {
  return file.size <= maxSizeMB * 1024 * 1024;
}

/**
 * Comprehensive PDF file validation
 */
export function validatePDFFile(
  file: File,
  maxSizeMB: number = 10,
): PDFValidationResult {
  if (!isPDFFile(file)) {
    return {
      isValid: false,
      error: `Invalid file type: ${file.name}. Please upload a PDF file.`,
    };
  }

  if (!isValidFileSize(file, maxSizeMB)) {
    return {
      isValid: false,
      error: `File too large: ${file.name}. Maximum size is ${maxSizeMB}MB.`,
    };
  }

  return { isValid: true };
}

/**
 * Extracts text content from a PDF file using pdfjs-dist
 */
export async function extractPDFText(file: File): Promise<string> {
  const arrayBuffer = await file.arrayBuffer();

  // Use the legacy build for compatibility with Node.js
  const { getDocument } = await import("pdfjs-dist/legacy/build/pdf.mjs");

  const pdf = await getDocument({
    data: arrayBuffer,
    cMapUrl: undefined,
    cMapPacked: false,
    standardFontDataUrl: undefined,
    useWorkerFetch: false,
    isEvalSupported: false,
    useSystemFonts: true,
    disableFontFace: true,
    disableRange: false,
    disableStream: false,
    verbosity: 0,
  }).promise;

  let fullText = "";

  for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
    const page = await pdf.getPage(pageNum);
    const textContent = await page.getTextContent();

    const pageText = textContent.items
      .map((item: any) => item.str || "")
      .join(" ");

    fullText += pageText + "\n";

    page.cleanup();
  }

  const result = fullText.trim();
  if (result.length === 0) {
    throw new Error("No text content found in PDF");
  }
  return result;
}

/**
 * Extracts email addresses from text using regex
 */
export function extractEmailsFromText(text: string): string[] {
  const emailRegex = /\S+@\S+\.\S+/g;
  const matches = text.match(emailRegex);
  return matches
    ? [...new Set(matches.map((email) => email.toLowerCase()))]
    : [];
}

/**
 * Validates PDF content for promotion requirements
 */
export async function validatePDFContent(
  file: File,
  options: PDFContentValidationOptions,
): Promise<PDFContentValidationResult> {
  try {
    const extractedText = await extractPDFText(file);
    const extractedEmails = extractEmailsFromText(extractedText);

    const userEmailLower = options.userEmail.toLowerCase();
    const hasUserEmail = extractedEmails.some((email) =>
      email.includes(userEmailLower),
    );

    if (!hasUserEmail) {
      return {
        isValid: false,
        error: `Requested email not found in invoice.`,
      };
    }

    if (options.requiredSenderEmail) {
      const requiredEmailLower = options.requiredSenderEmail.toLowerCase();
      const hasRequiredSender = extractedEmails.some((email) =>
        email.includes(requiredEmailLower),
      );

      if (!hasRequiredSender) {
        return {
          isValid: false,
          error: `Sender email ${options.requiredSenderEmail} not found.`,
        };
      }
    }

    return {
      isValid: true,
    };
  } catch (error) {
    return {
      isValid: false,
      error: `Failed to validate PDF content: ${error instanceof Error ? error.message : "Unknown error"}`,
    };
  }
}

/**
 * Formats file size in a human-readable format
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}
