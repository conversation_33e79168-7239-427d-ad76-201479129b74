import { withAuth } from "../.server/auth";
import { WindsurfPage } from "app/components/promotions/WindsurfPage";
import { getFeatureFlag } from "app/feature-flags/feature-flags.client";
import { SimpleHeader } from "app/components/navigation/BaseHeader";
import { Box } from "@radix-ui/themes";
import { useNavigate } from "@remix-run/react";
import { useEffect } from "react";
import { isBrowser } from "app/utils/isomorphic";

export const loader = withAuth(
  async () => {
    return Response.json({});
  },
  {
    adminOnly: false,
  },
);

export default function Windsurf() {
  const navigate = useNavigate();
  const isWindsurfFeatureEnabled = getFeatureFlag(
    "customer_ui_windsurf_promotion_enabled",
  );

  useEffect(() => {
    if (!isWindsurfFeatureEnabled) {
      navigate("/account/subscription");
    }
  }, [isWindsurfFeatureEnabled, navigate]);

  if (!isBrowser() || !isWindsurfFeatureEnabled) {
    return null;
  }

  return (
    <>
      <SimpleHeader />
      <Box
        style={{
          maxWidth: "1200px",
          margin: "0 auto",
          width: "100%",
          padding: "0 32px",
        }}
      >
        <WindsurfPage />
      </Box>
    </>
  );
}
