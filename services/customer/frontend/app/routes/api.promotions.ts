import { getFeatureFlag } from "app/feature-flags/feature-flags.server";
import { withApiRoute } from "../.server/auth";
import { AuthCentralClient } from "../.server/grpc/auth-central";
import { PromotionIdSchema } from "../schemas/promotions";
import { validatePDFContent, validatePDFFile } from "../utils/pdf-validation";

// GET /api/promotions - Evaluate promotion eligibility
export const loader = withApiRoute(
  async ({ user, request, log }) => {
    const start = Date.now();
    const isWindsurfFeatureEnabled = await getFeatureFlag(
      "customer_ui_windsurf_promotion_enabled",
      {
        tenantId: user.tenantId,
        shardNamespace: user.shardNamespace,
      },
    );
    if (!isWindsurfFeatureEnabled) {
      const status = 403;
      log.error("Windsurf feature is disabled", {
        status_code: status,
        duration_ms: Date.now() - start,
      });
      return Response.json(
        { error: "This promotion is not available" },
        { status },
      );
    }
    const url = new URL(request.url);
    const promotionIdParam = url.searchParams.get("id");

    log.info("Evaluating promotion eligibility", {
      promotion_id: promotionIdParam,
    });

    if (!promotionIdParam) {
      const status = 400;
      log.error("Missing id parameter", undefined, {
        status_code: status,
        duration_ms: Date.now() - start,
      });
      return Response.json({ error: "id parameter is required" }, { status });
    }

    try {
      PromotionIdSchema.parse(promotionIdParam);
    } catch (validationError) {
      const status = 400;
      log.error("Invalid promotion id format", validationError, {
        promotion_id: promotionIdParam,
        status_code: status,
        duration_ms: Date.now() - start,
      });
      return Response.json({ error: "Invalid id format" }, { status });
    }

    try {
      const authCentralClient = AuthCentralClient.getInstance();
      const response = await authCentralClient.evaluatePromotionEligibility(
        user,
        promotionIdParam,
      );

      const status = 200;
      log.info("Promotion eligibility evaluated successfully", {
        promotion_id: promotionIdParam,
        promotion_status: response.promotionStatus,
        status_code: status,
        duration_ms: Date.now() - start,
      });

      return Response.json({
        promotionStatus: response.promotionStatus,
      });
    } catch (error) {
      const status = 500;
      log.error("Failed to evaluate promotion eligibility", error, {
        promotion_id: promotionIdParam,
        status_code: status,
        duration_ms: Date.now() - start,
      });
      return Response.json({ error: "Internal server error" }, { status });
    }
  },
  {
    adminOnly: false,
  },
);

// POST /api/promotions - Process promotion
export const action = withApiRoute(
  async ({ user, request, log }) => {
    const start = Date.now();
    const isWindsurfFeatureEnabled = await getFeatureFlag(
      "customer_ui_windsurf_promotion_enabled",
      {
        tenantId: user.tenantId,
        shardNamespace: user.shardNamespace,
      },
    );
    if (!isWindsurfFeatureEnabled) {
      const status = 403;
      log.error("Windsurf feature is disabled", {
        status_code: status,
        duration_ms: Date.now() - start,
      });
      return Response.json(
        { error: "This promotion is not available" },
        { status },
      );
    }

    const formData = await request.formData();
    const promotionIdParam = formData.get("id");
    const file = formData.get("file") as File | null;

    log.info("Processing promotion", {
      promotion_id: promotionIdParam,
      has_file: !!file,
      file_size: file?.size,
    });

    if (!promotionIdParam) {
      const status = 400;
      log.error("Missing id parameter", undefined, {
        status_code: status,
        duration_ms: Date.now() - start,
      });
      return Response.json({ error: "id parameter is required" }, { status });
    }

    const promotionId = promotionIdParam.toString();
    try {
      PromotionIdSchema.parse(promotionId);
    } catch (validationError) {
      const status = 400;
      log.error("Invalid promotion id format", validationError, {
        promotion_id: promotionId,
        status_code: status,
        duration_ms: Date.now() - start,
      });
      return Response.json({ error: "Invalid id format" }, { status });
    }

    // All promotions currently require a file upload
    if (!file) {
      const status = 400;
      log.error("Missing file parameter", undefined, {
        promotion_id: promotionId,
        status_code: status,
        duration_ms: Date.now() - start,
      });
      return Response.json({ error: "File is required" }, { status });
    }

    if (file.size === 0) {
      const status = 400;
      log.error("Empty file provided", undefined, {
        promotion_id: promotionId,
        status_code: status,
        duration_ms: Date.now() - start,
      });
      return Response.json(
        { error: "Please select a valid invoice file" },
        { status },
      );
    }

    const validation = validatePDFFile(file, 1);
    if (!validation.isValid) {
      const status = 400;
      log.error("PDF validation failed", {
        promotion_id: promotionId,
        validation_error: validation.error,
        status_code: status,
        duration_ms: Date.now() - start,
      });
      return Response.json(
        { error: validation.error || "Invalid PDF file" },
        { status },
      );
    }

    log.info("File validation passed", {
      promotion_id: promotionId,
      file_size: file.size,
    });

    let requiredSenderEmail: string | undefined;
    if (promotionId === "windsurf_2025") {
      log.info("Validating Windsurf PDF content");
      requiredSenderEmail = "<EMAIL>";
    }

    const contentValidation = await validatePDFContent(file, {
      userEmail: user.email,
      requiredSenderEmail,
    });
    if (!contentValidation.isValid) {
      log.warn("PDF content validation failed", undefined, {
        validation_error:
          contentValidation.error || "Unknown validation error",
        duration_ms: Date.now() - start,
      });
      return Response.json(
        {
          error:
            "We couldn't verify your purchase. Please ensure you've uploaded the correct invoice.",
        },
        { status: 400 },
      );
    }

    log.info("PDF content validation passed");

    try {
      const authCentralClient = AuthCentralClient.getInstance();
      const response = await authCentralClient.processPromotion(
        user,
        promotionId,
      );

      if (!response.success) {
        const status = 400;
        log.error("Promotion processing returned failure", {
          promotion_id: promotionId,
          response: response,
          status_code: status,
          duration_ms: Date.now() - start,
        });
        return Response.json(
          { error: "Failed to process promotion" },
          { status },
        );
      }

      const status = 200;
      log.info("Promotion processed successfully", {
        promotion_id: promotionId,
        success: response.success,
        status_code: status,
        duration_ms: Date.now() - start,
      });

      return Response.json({
        success: response.success,
        message: file
          ? "Invoice uploaded successfully. We'll review your purchase and add 500 free user messages to your account once verified."
          : "Promotion processed successfully.",
      });
    } catch (error) {
      const status = 500;
      log.error("Failed to process promotion", error, {
        promotion_id: promotionId,
        status_code: status,
        duration_ms: Date.now() - start,
      });
      return Response.json({ error: "Internal server error" }, { status });
    }
  },
  {
    adminOnly: false,
  },
);
