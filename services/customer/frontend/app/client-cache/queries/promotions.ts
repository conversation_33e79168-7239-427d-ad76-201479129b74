import { queryOptions } from "../queryOptions";
import { PromotionEligibilityResponseSchema } from "../../schemas/promotions";
import { queryFetch } from "app/utils/query-fetch";

export const promotionEligibilityQueryOptions = queryOptions(
  (promotionId: string) => ({
    queryKey: ["promotion-eligibility", promotionId],
    queryFn: queryFetch(
      `/api/promotions?id=${encodeURIComponent(promotionId)}`,
      PromotionEligibilityResponseSchema,
    ),
  }),
);
