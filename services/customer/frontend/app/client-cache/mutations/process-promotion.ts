import { queryClient } from "../queryClient.client";
import { mutationOptions } from "../queryOptions";
import {
  ProcessPromotionRequestSchema,
  ProcessPromotionResponseSchema,
  type ProcessPromotionRequest,
} from "../../schemas/promotions";

export const processPromotion = mutationOptions({
  mutationFn: async (params: ProcessPromotionRequest) => {
    const { promotionId, file } = ProcessPromotionRequestSchema.parse(params);

    const formData = new FormData();
    formData.append("id", promotionId);

    if (file) {
      formData.append("file", file);
    }

    const response = await fetch("/api/promotions", {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || "Failed to process promotion");
    }

    const data = await response.json();
    return ProcessPromotionResponseSchema.parse(data);
  },
  onSuccess: (_data, variables) => {
    // Invalidate promotion eligibility queries to refresh status
    queryClient.invalidateQueries({
      queryKey: ["promotion-eligibility", variables.promotionId],
    });
  },
});
