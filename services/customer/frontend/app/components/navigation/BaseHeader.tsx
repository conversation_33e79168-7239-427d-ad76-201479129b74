import { <PERSON>, Button, Flex, Text, Skeleton } from "@radix-ui/themes";
import { Form, Link } from "@remix-run/react";
import { useQuery } from "@tanstack/react-query";
import { userQueryOptions } from "app/client-cache";
import type { ReactNode } from "react";

interface BaseHeaderProps {
  children?: ReactNode;
  containerWidth?: string;
  className?: string;
  sticky?: boolean;
}

export function BaseHeader({
  children,
  containerWidth = "1200px",
  className = "",
  sticky = false,
}: BaseHeaderProps) {
  const { data: userData, isLoading: userDataIsLoading } =
    useQuery(userQueryOptions);

  // Extract data from query with defaults for loading states
  const email = userData?.email ?? "";

  return (
    <Box className={`base-header-container ${className}`}>
      <style scoped>
        {`
          /* Scoped styles for BaseHeader component */
          :scope.base-header-container {
            border-radius: 0;
            overflow: hidden;
            padding: 18px 0;
            width: 100%;
            /* Prevent layout shift during hydration */
            contain: layout style;
            ${
              sticky
                ? `
              position: sticky;
              top: 0;
              z-index: 100;
              backdrop-filter: blur(10px);
              -webkit-backdrop-filter: blur(10px);
              background: rgba(255, 255, 255, 0.95);
            `
                : ""
            }
          }

          :scope .base-header-content {
            margin: 0 auto;
            width: 100%;
            padding: 0 32px;
            /* Ensure consistent layout during loading */
            min-height: 38px;
          }

          :scope .base-header-logo-section {
            position: relative;
          }

          :scope .base-header-logo {
            width: 140px;
            height: 38px;
          }

          :scope .base-header-logo-link {
            display: inline-block;
            transition: opacity 0.2s ease;
            cursor: pointer;
          }

          :scope .base-header-logo-link:hover {
            opacity: 0.8;
          }

          /* Email styles */
          :scope .base-header-email {
            opacity: 0.9;
            font-weight: 500;
            font-size: 15px;
          }

          /* Logout button styles */
          :scope .base-header-logout-button {
            transition: all 0.2s ease;
            font-weight: 600;
            font-size: 15px;
            opacity: 0.9;
            padding: 8px 16px;
          }

          /* Right section container to prevent layout shift */
          :scope .base-header-right-section {
            display: flex;
            align-items: center;
            gap: 12px;
            min-width: 200px; /* Prevent collapse during loading */
            justify-content: flex-end;
          }

          /* Logo section styles */
          :scope .base-header-logo-section {
            position: relative;
          }
        `}
      </style>
      {/* Container to align content with page width */}
      <Box className="base-header-content" style={{ maxWidth: containerWidth }}>
        <Flex
          direction={{ initial: "column", sm: "row" }}
          justify="between"
          align={{ initial: "start", sm: "center" }}
          gap={{ initial: "3", sm: "0" }}
        >
          <Flex
            align="center"
            gap="4"
            py="1"
            className="base-header-logo-section"
          >
            <Link to="/" className="base-header-logo-link">
              <img
                src="/augment-logo.svg"
                alt="Augment Logo"
                className="base-header-logo"
              />
            </Link>
            {/* Additional content can be inserted here (like plan info) */}
            {children}
          </Flex>
          <Flex align="center" gap="3" className="base-header-right-section">
            {email && !userDataIsLoading ? (
              <Text size="2" color="gray" className="base-header-email">
                {email}
              </Text>
            ) : (
              <Skeleton width="120px" height="15px" />
            )}
            <Box>
              <Form action="/logout">
                <Button
                  size="2"
                  color="gray"
                  variant="soft"
                  type="submit"
                  className="base-header-logout-button"
                >
                  Logout
                </Button>
              </Form>
            </Box>
          </Flex>
        </Flex>
      </Box>
    </Box>
  );
}

// Simple header for pages without navigation
export function SimpleHeader() {
  return (
    <Box style={{ marginBottom: "24px" }}>
      <BaseHeader sticky={true} />
    </Box>
  );
}

export default BaseHeader;
