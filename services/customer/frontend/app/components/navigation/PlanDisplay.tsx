import {
  LockClosedIcon,
  RocketIcon,
  PersonIcon,
  ClockIcon,
  LightningBoltIcon,
} from "@radix-ui/react-icons";
import { Flex, Skeleton, Text } from "@radix-ui/themes";
import { useLocation } from "@remix-run/react";
import type { PlanOptionSchema, UserPlanSchema } from "app/schemas/plan";
import { useQuery } from "@tanstack/react-query";
import {
  userQueryOptions,
  subscriptionQueryOptions,
  planChangeInProgressAtom,
  subscriptionCreationPendingAtom,
} from "app/client-cache";
import { capitalize } from "app/utils/string";
import { useAtomValue } from "jotai";

const getPlanBadgeConfig = (
  tier: UserPlanSchema["name"] | undefined,
  plan: PlanOptionSchema["id"] | undefined,
) => {
  if (!tier) {
    return null;
  }

  if (tier == "enterprise") {
    return {
      color: "gray",
      name: "Enterprise",
      icon: LockClosedIcon,
      cssColor: "var(--gray-12)",
    };
  }

  if (!plan) {
    return null;
  }

  // TODO(cam): use the color field on the plan instead of the id
  const config = {
    orb_trial_plan: {
      name: "Trial",
      icon: ClockIcon,
      color: "orange",
      cssColor: "var(--orange-8)",
    },
    orb_community_plan: {
      name: "Community",
      icon: PersonIcon,
      color: "sky",
      cssColor: "var(--sky-8)",
    },
    orb_developer_plan: {
      name: "Developer",
      icon: RocketIcon,
      color: "indigo",
      cssColor: "var(--indigo-9)",
    },
    orb_pro_plan: {
      name: "Pro",
      icon: RocketIcon,
      color: "purple",
      cssColor: "var(--purple-9)",
    },
    orb_max_plan: {
      name: "Max",
      icon: LightningBoltIcon,
      color: "amber",
      cssColor: "var(--amber-10)",
    },
  } as const;

  return config[plan as keyof typeof config];
};

// Component for displaying plan information in header
export function PlanDisplay() {
  const location = useLocation();
  const { data: userData, isLoading: userDataIsLoading } =
    useQuery(userQueryOptions);
  const { data: subscriptionData, isLoading: subscriptionIsLoading } = useQuery(
    subscriptionQueryOptions,
  );

  const isPlanChangeInProgress = useAtomValue(planChangeInProgressAtom);
  const isSubscriptionCreationPending = useAtomValue(
    subscriptionCreationPendingAtom,
  );

  const tenantTier = userData?.tenantTier;
  const planId = subscriptionData?.planId;

  // Determine if there's a pending plan change
  const isPending = isPlanChangeInProgress || isSubscriptionCreationPending;

  const planConfig = getPlanBadgeConfig(tenantTier, planId);
  const planName = planConfig?.name;
  const PlanIcon = planConfig?.icon;

  return (
    <Flex align="center" gap="3">
      <Flex
        align="center"
        gap="1"
        className={`topnav-plan-section ${location.pathname.includes("/select-plan") ? "hidden" : ""}`}
      >
        {tenantTier &&
        PlanIcon &&
        planConfig &&
        !userDataIsLoading &&
        !isPending &&
        (!subscriptionIsLoading || tenantTier === "enterprise") ? (
          <>
            <PlanIcon style={{ color: planConfig.cssColor }} />
            <Text
              size="2"
              weight="bold"
              className={`topnav-plan-text-real ${
                isPending
                  ? "topnav-plan-text-pending"
                  : "topnav-plan-text-gradient"
              }`}
              style={
                {
                  "--plan-color": planConfig.cssColor,
                } as React.CSSProperties
              }
            >
              {capitalize(planName || "")}
            </Text>
          </>
        ) : (
          <>
            <Skeleton width="16px" height="16px" />
            <Skeleton width="80px" height="15px" />
          </>
        )}
      </Flex>
    </Flex>
  );
}
