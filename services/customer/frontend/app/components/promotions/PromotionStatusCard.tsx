import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, Card } from "@radix-ui/themes";
import { Link } from "@remix-run/react";
import {
  CheckCircledIcon,
  ExclamationTriangleIcon,
  RocketIcon,
  ArrowRightIcon,
} from "@radix-ui/react-icons";
import { PromotionStatus } from "~services/auth/central/server/auth_entities_pb";
import { USAGE_UNITS } from "app/data/constants";
import { capitalize } from "app/utils/string";

export interface PromotionStatusCardProps {
  status: PromotionStatus | null;
  creditAmount: number;
  promotionName: string;
}

export function PromotionStatusCard({
  status,
  creditAmount,
  promotionName,
}: PromotionStatusCardProps) {
  const sharedStyles = (
    <style scoped>{`
      :scope .promotion-bullet-list {
        margin: 0;
        padding-left: var(--ds-spacing-4_5);
        color: var(--ds-color-neutral-11);
        font-size: var(--ds-font-size-2);
        line-height: 1.4;
        margin-bottom: var(--ds-spacing-4);
        list-style-type: disc;
        list-style-position: outside;
      }

      :scope .promotion-bullet-list li {
        margin-bottom: var(--ds-spacing-1);
      }

      :scope .promotion-bullet-list li:last-child {
        margin-bottom: 0;
      }
    `}</style>
  );

  if (status === PromotionStatus.INELIGIBLE) {
    return (
      <>
        <Card
          style={{
            marginBottom: "var(--ds-spacing-5)",
            backgroundColor: "var(--ds-color-warning-2)",
            border: "1px solid var(--ds-color-warning-6)",
          }}
          size="3"
        >
          <Flex align="center" gap="4">
            <Box
              style={{
                color: "var(--ds-color-warning-9)",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                minWidth: "var(--ds-spacing-6)",
                height: "var(--ds-spacing-6)",
                padding: "var(--ds-spacing-1)",
              }}
            >
              <ExclamationTriangleIcon width="24" height="24" />
            </Box>
            <Box>
              <Text
                size="4"
                weight="medium"
                style={{
                  display: "block",
                  marginBottom: "var(--ds-spacing-1)",
                }}
              >
                Not Eligible for This Promotion
              </Text>
              <Text
                size="2"
                style={{
                  color: "var(--ds-color-neutral-11)",
                  lineHeight: "1.4",
                }}
              >
                This special welcome offer is only available to new individual
                users who have recently signed up. Don&apos;t worry - you can
                still enjoy all of Augment&apos;s powerful features!
              </Text>
            </Box>
          </Flex>
        </Card>
        {sharedStyles}
      </>
    );
  }

  if (status === PromotionStatus.ENROLLED) {
    return (
      <>
        <Card
          style={{
            marginBottom: "var(--ds-spacing-5)",
            backgroundColor: "var(--ds-color-success-2)",
            border: "1px solid var(--ds-color-success-6)",
          }}
          size="3"
        >
          <Flex direction="column" gap="3">
            <Flex align="center" gap="4">
              <Box
                style={{
                  color: "var(--ds-color-success-9)",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  minWidth: "var(--ds-spacing-6)",
                  height: "var(--ds-spacing-6)",
                  padding: "var(--ds-spacing-1)",
                }}
              >
                <CheckCircledIcon width="24" height="24" />
              </Box>
              <Box>
                <Text
                  size="4"
                  weight="medium"
                  style={{
                    display: "block",
                    marginBottom: "var(--ds-spacing-1)",
                  }}
                >
                  Welcome Benefit Applied!
                </Text>
                <Text
                  size="2"
                  style={{
                    color: "var(--ds-color-neutral-11)",
                    lineHeight: "1.4",
                    marginBottom: "var(--ds-spacing-4)",
                  }}
                >
                  Great news! You&apos;ve received your welcome benefit.{" "}
                  {creditAmount} free {USAGE_UNITS} have been added to your
                  account.
                </Text>
              </Box>
            </Flex>
            <Box style={{ paddingLeft: "var(--ds-spacing-8)" }}>
              <ul className="promotion-bullet-list">
                <li>
                  Your {USAGE_UNITS} are valid for one year from when they were
                  added
                </li>
                <li>
                  Continue using any remaining {USAGE_UNITS} when you upgrade to
                  any plan after your trial
                </li>
                <li>
                  {capitalize(USAGE_UNITS)} cover a chat request or a full
                  end-to-end agent message
                </li>
              </ul>
            </Box>
          </Flex>
          <Box style={{ paddingTop: "var(--ds-spacing-4)" }}>
            <Link to="/">
              <Button variant="soft" size="2">
                Home
                <ArrowRightIcon width="16" height="16" />
              </Button>
            </Link>
          </Box>
        </Card>
        {sharedStyles}
      </>
    );
  }

  // Default case for ELIGIBLE, UNKNOWN, or null status
  return (
    <>
      <Card
        style={{
          marginBottom: "var(--ds-spacing-5)",
          backgroundColor: "var(--ds-color-accent-2)",
          border: "1px solid var(--ds-color-accent-6)",
        }}
        size="3"
      >
        <Flex direction="column" gap="3">
          <Flex align="center" gap="4">
            <Box
              style={{
                color: "var(--ds-color-success-9)",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                minWidth: "var(--ds-spacing-6)",
                height: "var(--ds-spacing-6)",
                padding: "var(--ds-spacing-1)",
              }}
            >
              <RocketIcon width="24" height="24" />
            </Box>
            <Box>
              <Text
                size="4"
                weight="medium"
                style={{
                  display: "block",
                  marginBottom: "var(--ds-spacing-1)",
                }}
              >
                Get Started with Free {USAGE_UNITS}
              </Text>
              <Text
                size="2"
                style={{
                  color: "var(--ds-color-neutral-11)",
                  lineHeight: "1.4",
                  marginBottom: "var(--ds-spacing-4)",
                }}
              >
                Get{" "}
                <strong>
                  {creditAmount} free {USAGE_UNITS}
                </strong>{" "}
                as our welcome gift for switching from {promotionName}
              </Text>
            </Box>
          </Flex>
          <Box style={{ paddingLeft: "var(--ds-spacing-8)" }}>
            <ul className="promotion-bullet-list">
              <li>
                {USAGE_UNITS} are valid for one year from the date they&apos;re
                added to your account
              </li>
              <li>
                Use them during your free trial and continue using any remaining{" "}
                {USAGE_UNITS} when you upgrade
              </li>
              <li>
                {capitalize(USAGE_UNITS)} cover a chat request or a full
                end-to-end agent message
              </li>
            </ul>
          </Box>
        </Flex>
      </Card>
      {sharedStyles}
    </>
  );
}
