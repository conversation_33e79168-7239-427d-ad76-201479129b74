import { Box, Text, But<PERSON>, Flex } from "@radix-ui/themes";
import { useState, useRef } from "react";
import type React from "react";
import { validatePDFFile } from "app/utils/pdf-validation";
import { formatFileSize } from "app/utils/number";

export interface PDFUploadComponentProps {
  onFileSelect: (file: File | null) => void;
  selectedFile: File | null;
  isSubmitting?: boolean;
  onSubmit: (file: File) => void;
  maxSizeMB?: number;
  disabled?: boolean;
}

export function PDFUploadComponent({
  onFileSelect,
  selectedFile,
  isSubmitting = false,
  onSubmit,
  maxSizeMB = 10,
  disabled = false,
}: PDFUploadComponentProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileValidation = (file: File) => {
    const validation = validatePDFFile(file, maxSizeMB);

    if (!validation.isValid) {
      setValidationError(validation.error || "Invalid file");
      onFileSelect(null);
      return false;
    }

    setValidationError(null);
    onFileSelect(file);
    return true;
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    setValidationError(null);

    if (file) {
      handleFileValidation(file);
    } else {
      onFileSelect(null);
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    if (!disabled) {
      setIsDragging(true);
      setValidationError(null);
    }
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(false);

    if (disabled) return;

    const files = event.dataTransfer.files;
    if (files.length > 0) {
      handleFileValidation(files[0]);
    }
  };

  const handleDropZoneClick = () => {
    if (!disabled) {
      setValidationError(null);
      fileInputRef.current?.click();
    }
  };

  const handleDropZoneKeyDown = (event: React.KeyboardEvent) => {
    if (!disabled && (event.key === "Enter" || event.key === " ")) {
      event.preventDefault();
      handleDropZoneClick();
    }
  };

  const handleSubmitClick = () => {
    if (selectedFile && !isSubmitting && !disabled) {
      onSubmit(selectedFile);
    }
  };

  return (
    <Box>
      <style scoped>
        {`
          :scope {
            .drag-drop-zone {
              border: 2px dashed var(--ds-color-border-default);
              border-radius: var(--ds-radius-4);
              padding: var(--ds-spacing-8);
              text-align: center;
              cursor: pointer;
              transition: all 0.2s ease;
              background-color: var(--ds-color-background-default);
              min-height: 200px;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              gap: var(--ds-spacing-3);
            }

            .drag-drop-zone:hover:not(.disabled) {
              border-color: var(--ds-color-accent-7);
              background-color: var(--ds-color-accent-2);
            }

            .drag-drop-zone.is-dragging {
              border-color: var(--ds-color-accent-9);
              background-color: var(--ds-color-accent-3);
              border-style: solid;
            }

            .drag-drop-zone.has-file {
              border-color: var(--ds-color-success-7);
              background-color: var(--ds-color-success-2);
            }

            .drag-drop-zone.has-error {
              border-color: var(--ds-color-error-7);
              background-color: var(--ds-color-error-2);
            }

            .drag-drop-zone.disabled {
              opacity: 0.5;
              cursor: not-allowed;
            }

            .file-input {
              display: none;
            }

            .validation-error {
              background-color: var(--ds-color-error-2);
              border: 1px solid var(--ds-color-error-6);
              border-radius: var(--ds-radius-2);
              padding: var(--ds-spacing-2);
              margin-top: var(--ds-spacing-2);
              color: var(--ds-color-error-11);
            }

            .upload-icon {
              width: 48px;
              height: 48px;
              color: var(--ds-color-neutral-9);
              margin-bottom: var(--ds-spacing-2);
            }

            .subtle-text {
              color: var(--ds-color-neutral-11);
              font-size: var(--ds-font-size-2);
            }
          }
        `}
      </style>

      <Flex direction="column" gap="4">
        <Box>
          <div
            className={`drag-drop-zone ${isDragging ? "is-dragging" : ""} ${
              selectedFile ? "has-file" : ""
            } ${validationError ? "has-error" : ""} ${disabled ? "disabled" : ""}`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={handleDropZoneClick}
            onKeyDown={handleDropZoneKeyDown}
            role="button"
            tabIndex={disabled ? -1 : 0}
            aria-label="Upload PDF file"
            aria-disabled={disabled}
          >
            <svg
              className="upload-icon"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
              />
            </svg>

            {selectedFile ? (
              <div>
                <Text
                  size="4"
                  weight="medium"
                  style={{ display: "block", marginBottom: "8px" }}
                >
                  File Selected
                </Text>
                <Text
                  size="3"
                  style={{ display: "block", marginBottom: "4px" }}
                >
                  {selectedFile.name}
                </Text>
                <Text size="2" className="subtle-text">
                  {formatFileSize(selectedFile.size)}
                </Text>
                {!disabled && (
                  <Text
                    size="2"
                    className="subtle-text"
                    style={{ display: "block", marginTop: "8px" }}
                  >
                    Click to change file or drag a new one here
                  </Text>
                )}
              </div>
            ) : (
              <div>
                <Text
                  size="4"
                  weight="medium"
                  style={{ display: "block", marginBottom: "8px" }}
                >
                  {isDragging
                    ? "Drop your PDF here"
                    : "Drag & drop your PDF here"}
                </Text>
                <Text
                  size="3"
                  className="subtle-text"
                  style={{ display: "block", marginBottom: "8px" }}
                >
                  or click to browse files
                </Text>
                <Text size="2" className="subtle-text">
                  PDF files only, max {maxSizeMB}MB
                </Text>
              </div>
            )}
          </div>

          <input
            ref={fileInputRef}
            type="file"
            name="invoice"
            accept=".pdf"
            onChange={handleFileChange}
            className="file-input"
            disabled={disabled}
          />

          {validationError && (
            <div className="validation-error">
              <Text size="2">{validationError}</Text>
            </div>
          )}
        </Box>

        <Flex justify="end">
          <Button
            type="button"
            onClick={handleSubmitClick}
            disabled={!selectedFile || isSubmitting || disabled}
            variant="solid"
          >
            {isSubmitting ? "Uploading..." : "Upload Invoice"}
          </Button>
        </Flex>
      </Flex>
    </Box>
  );
}
